# Deep Research MCP Server - Setup Guide

## Quick Start

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Build the Project**
   ```bash
   npm run build
   ```

4. **Test Setup**
   ```bash
   node test-setup.js
   ```

5. **Start the Server**
   ```bash
   npm start
   ```

## Required API Keys

### For AI Processing (Choose One)

#### OpenAI (Recommended)
- Visit: https://platform.openai.com
- Create API key
- Set in .env: `AI_PROVIDER=openai` and `AI_API_KEY=sk-...`

#### Anthropic
- Visit: https://console.anthropic.com
- Create API key  
- Set in .env: `AI_PROVIDER=anthropic` and `AI_API_KEY=sk-ant-...`

### For Web Search (Choose One)

#### Serper (Recommended - Free Tier)
- Visit: https://serper.dev
- Sign up for free account (100 searches/month)
- Set in .env: `SEARCH_PROVIDER=serper` and `SEARCH_API_KEY=your_key`

#### Google Custom Search
- Create project at https://console.cloud.google.com
- Enable Custom Search API
- Create Custom Search Engine at https://cse.google.com
- Set in .env: `SEARCH_PROVIDER=google`, `SEARCH_API_KEY=your_key`, `GOOGLE_SEARCH_ENGINE_ID=your_id`

#### DuckDuckGo (Free, No API Key)
- Set in .env: `SEARCH_PROVIDER=duckduckgo`
- No API key required (limited functionality)

## Claude Desktop Integration

Add to your Claude Desktop configuration (`~/Library/Application Support/Claude/claude_desktop_config.json` on macOS):

```json
{
  "mcpServers": {
    "deep-research": {
      "command": "node",
      "args": ["path/to/deep-research-mcp-server/dist/index.js"],
      "env": {
        "AI_PROVIDER": "openai",
        "AI_API_KEY": "your_openai_api_key",
        "SEARCH_PROVIDER": "serper", 
        "SEARCH_API_KEY": "your_serper_api_key"
      }
    }
  }
}
```

## Usage Examples

### Basic Research
```
Use the deep_research tool to research "latest developments in quantum computing 2024"
```

### Focused Research
```
Use deep_research with query "AI impact on healthcare" and focus areas: ["medical diagnosis", "drug discovery", "patient care"]
```

### Check Progress
```
Use get_research_status with the task ID to check progress
```

### Continue Research
```
Use continue_research to refine or expand existing research with additional instructions
```

## Features

✅ **Multi-step Planning** - AI creates detailed research plans
✅ **Web Search Integration** - Multiple search providers supported  
✅ **Content Extraction** - Advanced web scraping with Puppeteer
✅ **AI Analysis** - Iterative reasoning through findings
✅ **Report Generation** - Comprehensive markdown reports
✅ **Asynchronous Processing** - Long-running background tasks
✅ **Progress Tracking** - Real-time status updates
✅ **Persistent Storage** - SQLite database for task history

## Troubleshooting

### Build Errors
- Ensure Node.js 18+ is installed
- Run `npm install` to install dependencies
- Check TypeScript compilation with `npm run build`

### API Key Issues
- Verify API keys are correct format
- Check API key permissions and credits
- Test with `node test-setup.js`

### Search Issues
- Try different search providers
- Check API rate limits
- Verify network connectivity

### Browser Issues
- Ensure Puppeteer can run (may need additional dependencies on Linux)
- Check if headless mode works: `BROWSER_HEADLESS=true`
- Try different timeout values: `BROWSER_TIMEOUT=60000`

## Development

### Scripts
- `npm run build` - Build TypeScript
- `npm run dev` - Development mode with hot reload
- `npm test` - Run tests (when implemented)
- `npm run lint` - Lint code
- `npm run format` - Format code

### Architecture
- **index.ts** - Main MCP server and request handling
- **planner.ts** - AI-powered research planning
- **searcher.ts** - Multi-provider web search
- **browser.ts** - Web content extraction
- **reasoner.ts** - AI analysis and decision making
- **synthesizer.ts** - Report generation
- **storage.ts** - SQLite persistence layer
- **types.ts** - TypeScript type definitions
- **utils.ts** - Utility functions

## Support

For issues:
1. Check this setup guide
2. Run `node test-setup.js` to diagnose problems
3. Check the logs for error messages
4. Verify API keys and network connectivity

## License

MIT License - see LICENSE file for details.
