#!/usr/bin/env node

/**
 * Test script to verify Deep Research MCP Server setup
 * Run with: node test-setup.js
 */

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🔍 Deep Research MCP Server - Setup Test\n');

// Test 1: Check required files
console.log('📁 Checking required files...');
const requiredFiles = [
  'package.json',
  'tsconfig.json',
  'src/index.ts',
  'src/types.ts',
  'src/storage.ts',
  'src/searcher.ts',
  'src/browser.ts',
  'src/planner.ts',
  'src/reasoner.ts',
  'src/synthesizer.ts',
  'src/utils.ts'
];

let filesOk = true;
for (const file of requiredFiles) {
  const exists = existsSync(join(__dirname, file));
  console.log(`  ${exists ? '✅' : '❌'} ${file}`);
  if (!exists) filesOk = false;
}

if (!filesOk) {
  console.log('\n❌ Some required files are missing. Please check your installation.');
  process.exit(1);
}

// Test 2: Check environment configuration
console.log('\n⚙️  Checking environment configuration...');

const requiredEnvVars = [
  { name: 'AI_PROVIDER', required: true },
  { name: 'AI_MODEL', required: true },
  { name: 'AI_API_KEY', required: true },
  { name: 'SEARCH_PROVIDER', required: true },
  { name: 'SEARCH_API_KEY', required: false }, // Some providers don't need keys
];

let envOk = true;
for (const { name, required } of requiredEnvVars) {
  const value = process.env[name];
  const hasValue = value && value.length > 0;
  
  if (required && !hasValue) {
    console.log(`  ❌ ${name} - Missing (required)`);
    envOk = false;
  } else if (hasValue) {
    // Mask API keys for security
    const displayValue = name.includes('KEY') ? 
      value.substring(0, 8) + '...' : value;
    console.log(`  ✅ ${name} - ${displayValue}`);
  } else {
    console.log(`  ⚠️  ${name} - Not set (optional)`);
  }
}

// Test 3: Validate AI provider configuration
console.log('\n🤖 Validating AI provider configuration...');
const aiProvider = process.env.AI_PROVIDER;
const aiModel = process.env.AI_MODEL;
const aiApiKey = process.env.AI_API_KEY;

if (aiProvider === 'openai') {
  if (!aiApiKey || !aiApiKey.startsWith('sk-')) {
    console.log('  ❌ OpenAI API key should start with "sk-"');
    envOk = false;
  } else {
    console.log('  ✅ OpenAI configuration looks valid');
  }
} else if (aiProvider === 'anthropic') {
  if (!aiApiKey || !aiApiKey.startsWith('sk-ant-')) {
    console.log('  ❌ Anthropic API key should start with "sk-ant-"');
    envOk = false;
  } else {
    console.log('  ✅ Anthropic configuration looks valid');
  }
} else if (aiProvider === 'ollama' || aiProvider === 'local') {
  console.log(`  ✅ ${aiProvider.charAt(0).toUpperCase() + aiProvider.slice(1)} configuration looks valid`);
  if (aiProvider === 'ollama') {
    console.log('  ℹ️  Make sure Ollama is running: ollama serve');
    console.log('  ℹ️  And model is available: ollama pull deepseek-r1');
  }
} else {
  console.log(`  ❌ Unsupported AI provider: ${aiProvider}`);
  envOk = false;
}

// Test 4: Validate search provider configuration
console.log('\n🔍 Validating search provider configuration...');
const searchProvider = process.env.SEARCH_PROVIDER;
const searchApiKey = process.env.SEARCH_API_KEY;

switch (searchProvider) {
  case 'serper':
    if (!searchApiKey) {
      console.log('  ❌ Serper requires an API key');
      envOk = false;
    } else {
      console.log('  ✅ Serper configuration looks valid');
    }
    break;
  case 'google':
    if (!searchApiKey) {
      console.log('  ❌ Google Search requires an API key');
      envOk = false;
    } else if (!process.env.GOOGLE_SEARCH_ENGINE_ID) {
      console.log('  ❌ Google Search requires GOOGLE_SEARCH_ENGINE_ID');
      envOk = false;
    } else {
      console.log('  ✅ Google Search configuration looks valid');
    }
    break;
  case 'bing':
    if (!searchApiKey) {
      console.log('  ❌ Bing Search requires an API key');
      envOk = false;
    } else {
      console.log('  ✅ Bing Search configuration looks valid');
    }
    break;
  case 'duckduckgo':
    console.log('  ✅ DuckDuckGo configuration valid (no API key required)');
    break;
  default:
    console.log(`  ❌ Unsupported search provider: ${searchProvider}`);
    envOk = false;
}

// Test 5: Check Node.js version
console.log('\n🟢 Checking Node.js version...');
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion >= 18) {
  console.log(`  ✅ Node.js ${nodeVersion} (supported)`);
} else {
  console.log(`  ❌ Node.js ${nodeVersion} (requires 18+)`);
  envOk = false;
}

// Test 6: Check if .env file exists
console.log('\n📄 Checking configuration files...');
if (existsSync(join(__dirname, '.env'))) {
  console.log('  ✅ .env file found');
} else {
  console.log('  ⚠️  .env file not found (using environment variables)');
}

if (existsSync(join(__dirname, '.env.example'))) {
  console.log('  ✅ .env.example file found');
} else {
  console.log('  ⚠️  .env.example file not found');
}

// Final result
console.log('\n' + '='.repeat(50));
if (envOk && filesOk) {
  console.log('🎉 Setup test passed! Your Deep Research MCP Server is ready to use.');
  console.log('\nNext steps:');
  console.log('1. Build the project: npm run build');
  console.log('2. Start the server: npm start');
  console.log('3. Configure your MCP client (e.g., Claude Desktop)');
} else {
  console.log('❌ Setup test failed. Please fix the issues above.');
  console.log('\nCommon fixes:');
  console.log('1. Copy .env.example to .env and fill in your API keys');
  console.log('2. Install dependencies: npm install');
  console.log('3. Check that all required files are present');
  process.exit(1);
}

console.log('\n📚 For detailed setup instructions, see README.md');
console.log('🐛 For issues, visit: https://github.com/your-org/deep-research-mcp-server/issues');
