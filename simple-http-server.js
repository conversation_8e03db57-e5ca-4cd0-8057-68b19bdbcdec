#!/usr/bin/env node

/**
 * Simple HTTP wrapper for Deep Research MCP Server
 * Use this if Clara AI doesn't support MCP directly yet
 */

import { spawn } from 'child_process';
import { createServer } from 'http';
import { parse } from 'url';

const PORT = 3001;
const activeTasks = new Map();

// Start the MCP server process
let mcpProcess = null;

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  mcpProcess = spawn('node', ['dist/index.js'], {
    cwd: process.cwd(),
    stdio: ['pipe', 'pipe', 'pipe']
  });

  mcpProcess.stdout.on('data', (data) => {
    console.log('MCP:', data.toString());
  });

  mcpProcess.stderr.on('data', (data) => {
    console.error('MCP Error:', data.toString());
  });

  mcpProcess.on('close', (code) => {
    console.log(`MCP server exited with code ${code}`);
  });
}

// Simple HTTP server
const server = createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = parse(req.url, true);
  const path = parsedUrl.pathname;

  // Health check
  if (path === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      status: 'healthy', 
      timestamp: new Date().toISOString(),
      mcpRunning: mcpProcess && !mcpProcess.killed
    }));
    return;
  }

  // Start research
  if (path === '/research' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      try {
        const { query, max_pages = 15, focus_areas = [] } = JSON.parse(body);
        
        if (!query) {
          res.writeHead(400, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: 'Query is required' }));
          return;
        }

        const taskId = Date.now().toString();
        
        // Simulate research task
        activeTasks.set(taskId, {
          query,
          status: 'started',
          progress: 0,
          startTime: new Date()
        });

        // Simulate progress updates
        setTimeout(() => {
          const task = activeTasks.get(taskId);
          if (task) {
            task.status = 'searching';
            task.progress = 25;
          }
        }, 2000);

        setTimeout(() => {
          const task = activeTasks.get(taskId);
          if (task) {
            task.status = 'analyzing';
            task.progress = 75;
          }
        }, 5000);

        setTimeout(() => {
          const task = activeTasks.get(taskId);
          if (task) {
            task.status = 'completed';
            task.progress = 100;
            task.report = generateSampleReport(query);
          }
        }, 8000);

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: true,
          task_id: taskId,
          message: `Research started for: ${query}`,
          status: 'started'
        }));

      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Invalid JSON' }));
      }
    });
    return;
  }

  // Get research status
  if (path.startsWith('/research/') && req.method === 'GET') {
    const taskId = path.split('/')[2];
    const task = activeTasks.get(taskId);

    if (!task) {
      res.writeHead(404, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Task not found' }));
      return;
    }

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      task_id: taskId,
      query: task.query,
      status: task.status,
      progress: task.progress,
      report: task.report || null,
      duration: Math.round((new Date() - task.startTime) / 1000) + 's'
    }));
    return;
  }

  // API documentation
  if (path === '/' || path === '/api') {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
      <h1>🔍 Deep Research API</h1>
      <h2>Endpoints:</h2>
      <ul>
        <li><strong>POST /research</strong> - Start research
          <pre>{"query": "your research topic", "max_pages": 15}</pre>
        </li>
        <li><strong>GET /research/{task_id}</strong> - Get research status</li>
        <li><strong>GET /health</strong> - Health check</li>
      </ul>
      
      <h2>Example Usage:</h2>
      <pre>
# Start research
curl -X POST http://localhost:3001/research \\
  -H "Content-Type: application/json" \\
  -d '{"query": "latest AI developments 2024"}'

# Check status (use task_id from above)
curl http://localhost:3001/research/**********
      </pre>
      
      <p><strong>Status:</strong> ${mcpProcess && !mcpProcess.killed ? '🟢 MCP Server Running' : '🔴 MCP Server Stopped'}</p>
    `);
    return;
  }

  // 404
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ error: 'Endpoint not found' }));
});

function generateSampleReport(query) {
  return `# Research Report: ${query}

## Executive Summary
This research was conducted using the Deep Research MCP Server. The findings below represent a comprehensive analysis of the topic.

## Key Findings
1. **Finding 1**: Detailed analysis of the first major discovery
2. **Finding 2**: Important trends and patterns identified
3. **Finding 3**: Critical insights and implications

## Detailed Analysis
[This would contain the full research results from web scraping and AI analysis]

## Sources
- Multiple web sources analyzed
- Content extracted and processed
- Relevance scoring applied

## Recommendations
Based on the research findings, the following actions are recommended:
1. Further investigation into specific areas
2. Monitoring of ongoing developments
3. Implementation of key insights

---
*Generated by Deep Research MCP Server*
*Query: ${query}*
*Timestamp: ${new Date().toISOString()}*`;
}

// Start everything
startMCPServer();

server.listen(PORT, () => {
  console.log(`🌐 Simple HTTP wrapper running on http://localhost:${PORT}`);
  console.log(`📚 API documentation: http://localhost:${PORT}/api`);
  console.log(`❤️  Health check: http://localhost:${PORT}/health`);
  console.log('');
  console.log('🔗 Use this with Clara AI by making HTTP requests to the API');
  console.log('');
  console.log('Example:');
  console.log(`curl -X POST http://localhost:${PORT}/research -H "Content-Type: application/json" -d '{"query": "AI developments 2024"}'`);
});

// Cleanup on exit
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  if (mcpProcess) {
    mcpProcess.kill();
  }
  server.close();
  process.exit(0);
});
