{"name": "deep-research-mcp-server", "version": "1.0.0", "description": "A comprehensive MCP server for deep research capabilities similar to Google Gemini", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["mcp", "model-context-protocol", "research", "ai", "web-search", "deep-research"], "author": "Deep Research Team", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@modelcontextprotocol/sdk": "^1.0.0", "@types/node": "^20.0.0", "axios": "^1.6.0", "cheerio": "^1.0.0", "dotenv": "^16.0.0", "openai": "^4.0.0", "p-queue": "^8.0.0", "p-retry": "^6.0.0", "puppeteer": "^21.0.0", "sqlite3": "^5.1.0", "turndown": "^7.1.0", "uuid": "^9.0.0", "zod": "^3.22.0"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jest": "^29.0.0", "@types/turndown": "^5.0.5", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "prettier": "^3.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/deep-research-mcp-server.git"}, "bugs": {"url": "https://github.com/your-org/deep-research-mcp-server/issues"}, "homepage": "https://github.com/your-org/deep-research-mcp-server#readme"}