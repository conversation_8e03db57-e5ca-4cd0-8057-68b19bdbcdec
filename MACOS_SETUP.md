# 🍎 Deep Research MCP Server - macOS Setup Guide

## Perfect for Your Setup: Clara AI + Ollama + Brave + macOS

This guide is specifically optimized for your configuration:
- **macOS** (native performance)
- **<PERSON> AI** (MCP client)
- **Ollama + DeepSeek R1** (local AI)
- **<PERSON> Browser** (no conflicts)

## 🚀 Quick Start (5 minutes)

### 1. Prerequisites Check

```bash
# Verify Node.js (should be 18+)
node --version

# Verify Ollama is installed and running
ollama --version
ollama serve  # Keep this running in background

# Pull DeepSeek R1 model (if not already done)
ollama pull deepseek-r1
```

### 2. Install & Configure

```bash
# Clone and setup
git clone <your-repo-url>
cd deep-research-mcp-server

# Install dependencies
npm install

# Build the project
npm run build

# Test configuration
node test-setup.js
```

### 3. Configure for Ollama

The `.env` file is already optimized for your setup:

```env
# AI Provider - Using your local Ollama
AI_PROVIDER=ollama
AI_BASE_URL=http://localhost:11434/v1
AI_MODEL=deepseek-r1:latest
AI_API_KEY=not_required_for_local

# Search Provider - Free option
SEARCH_PROVIDER=duckduckgo

# Performance - Conservative for local AI
MAX_PAGES_PER_TASK=15
MAX_CONCURRENT_SEARCHES=2
```

### 4. Start the Server

```bash
npm start
```

You should see: `Deep Research MCP Server running on stdio`

## 🔌 Clara AI Integration

### Method 1: Direct MCP Connection

If Clara AI supports MCP servers directly, configure it to connect to:
- **Command**: `node`
- **Args**: `["path/to/deep-research-mcp-server/dist/index.js"]`
- **Working Directory**: `/path/to/deep-research-mcp-server`

### Method 2: Manual Integration

If Clara AI doesn't have direct MCP support yet, you can:

1. **Start the server** in one terminal:
   ```bash
   npm start
   ```

2. **Use the tools manually** by copying the research results and feeding them to Clara AI

## 🎯 Usage Examples

### Basic Research
```
Research "latest quantum computing breakthroughs 2024"
```

### Focused Research
```
Research "AI impact on healthcare" with focus on:
- Medical diagnosis accuracy
- Drug discovery
- Patient care automation
Limit to 20 pages, exclude social media sources
```

### Monitor Progress
```
Check status of research task [task-id]
```

## 🔧 macOS Optimizations

### Performance Tuning

**For M1/M2 Macs (Recommended):**
```env
# Optimized for Apple Silicon
MAX_PAGES_PER_TASK=20
MAX_CONCURRENT_SEARCHES=3
BROWSER_TIMEOUT=25000
```

**For Intel Macs:**
```env
# Conservative settings
MAX_PAGES_PER_TASK=15
MAX_CONCURRENT_SEARCHES=2
BROWSER_TIMEOUT=30000
```

### Browser Configuration

The server uses its own Chromium instance (via Puppeteer), so Brave won't interfere:

```env
# Browser settings (already optimized)
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000

# Optional: Use system Chrome if needed
# PUPPETEER_EXECUTABLE_PATH=/Applications/Google Chrome.app/Contents/MacOS/Google Chrome
```

## 🛠️ Troubleshooting

### Common Issues

**1. Ollama Connection Issues**
```bash
# Check if Ollama is running
ps aux | grep ollama

# Start Ollama if not running
ollama serve

# Test Ollama API
curl http://localhost:11434/api/tags
```

**2. Model Not Found**
```bash
# List available models
ollama list

# Pull DeepSeek R1 if missing
ollama pull deepseek-r1

# Or try alternative model
ollama pull llama2
# Then update .env: AI_MODEL=llama2:latest
```

**3. Permission Issues (macOS)**
```bash
# Fix permissions if needed
sudo chown -R $(whoami) ./deep-research-mcp-server
chmod +x ./node_modules/.bin/*
```

**4. Browser Issues**
```bash
# Install Chromium dependencies (rarely needed on macOS)
# Usually auto-handled by Puppeteer

# If issues persist, use system Chrome:
# Uncomment PUPPETEER_EXECUTABLE_PATH in .env
```

### Performance Issues

**If research is slow:**
1. **Reduce scope**: Set `MAX_PAGES_PER_TASK=10`
2. **Use faster model**: Try `llama2:7b` instead of `deepseek-r1`
3. **Increase timeout**: Set `BROWSER_TIMEOUT=45000`

**If Ollama is slow:**
1. **Check system resources**: Activity Monitor → CPU/Memory
2. **Reduce concurrent operations**: `MAX_CONCURRENT_SEARCHES=1`
3. **Consider cloud AI**: Switch to OpenAI for faster processing

## 🎯 Workflow with Clara AI

### Recommended Research Workflow

1. **Start Research**: Use MCP server to gather comprehensive data
2. **Feed to Clara AI**: Copy research results to Clara AI chat
3. **Deep Analysis**: Let DeepSeek R1 (via Clara AI) analyze findings
4. **Iterate**: Use continue_research for additional focus areas
5. **Synthesize**: Combine MCP research + Clara AI analysis

### Example Session

```bash
# Terminal 1: Start MCP server
npm start

# Terminal 2: Monitor Ollama
ollama ps

# Clara AI: Analyze the research results
# "Based on this research data, what are the key implications for..."
```

## 🔄 Updates & Maintenance

```bash
# Update dependencies
npm update

# Rebuild after changes
npm run build

# Update Ollama models
ollama pull deepseek-r1

# Test configuration
node test-setup.js
```

## 🆘 Getting Help

**If you encounter issues:**

1. **Check logs**: The server outputs detailed error messages
2. **Verify setup**: Run `node test-setup.js`
3. **Test Ollama**: `curl http://localhost:11434/api/tags`
4. **Check resources**: Activity Monitor for CPU/Memory usage
5. **Try simpler config**: Use DuckDuckGo + smaller page limits

**Common Clara AI + MCP patterns:**
- Background research while chatting
- Fact-checking and source verification
- Comprehensive analysis with citations
- Multi-step research refinement

## 🎉 You're All Set!

Your Deep Research MCP Server is now optimized for:
- ✅ **macOS performance**
- ✅ **Local Ollama AI processing**
- ✅ **Clara AI integration**
- ✅ **Privacy-focused research** (no external AI calls)
- ✅ **Brave browser compatibility**

The combination of Clara AI (DeepSeek R1) + Deep Research MCP Server provides research capabilities that rival Google Gemini Deep Research, all running locally on your Mac! 🚀
