import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import TurndownService from 'turndown';
import { WebContent, BrowsingError } from './types.js';
import { retry, getCurrentTimestamp, sanitizeText, isValidUrl } from './utils.js';

export class BrowserManager {
  private browser: Browser | null = null;
  private turndownService: TurndownService;
  private config: {
    headless: boolean;
    timeout: number;
    userAgent?: string;
  };

  constructor(config: { headless?: boolean; timeout?: number; userAgent?: string } = {}) {
    this.config = {
      headless: config.headless ?? true,
      timeout: config.timeout ?? 30000,
      userAgent: config.userAgent || undefined,
    };

    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      codeBlockStyle: 'fenced',
    });

    // Configure turndown to preserve important elements
    this.turndownService.addRule('preserveLinks', {
      filter: 'a',
      replacement: (content: string, node: any) => {
        const href = node.getAttribute('href');
        return href ? `[${content}](${href})` : content;
      },
    });
  }

  async initialize(): Promise<void> {
    if (this.browser) {
      return;
    }

    try {
      this.browser = await puppeteer.launch({
        headless: this.config.headless,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
        ],
      });
    } catch (error) {
      throw new BrowsingError(`Failed to initialize browser: ${(error as Error).message}`);
    }
  }

  async extractContent(url: string): Promise<WebContent> {
    if (!isValidUrl(url)) {
      throw new BrowsingError(`Invalid URL: ${url}`);
    }

    await this.initialize();

    return retry(async () => {
      const page = await this.createPage();
      
      try {
        await page.goto(url, {
          waitUntil: 'networkidle2',
          timeout: this.config.timeout,
        });

        // Wait for content to load
        await page.waitForTimeout(2000);

        // Extract page content
        const content = await page.evaluate(() => {
          // Remove script and style elements
          const scripts = document.querySelectorAll('script, style, nav, footer, aside, .advertisement, .ads');
          scripts.forEach(el => el.remove());

          // Try to find main content area
          const mainSelectors = [
            'main',
            'article',
            '[role="main"]',
            '.main-content',
            '.content',
            '.post-content',
            '.entry-content',
            '#content',
            '#main',
          ];

          let mainContent = null;
          for (const selector of mainSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent && element.textContent.trim().length > 200) {
              mainContent = element;
              break;
            }
          }

          // Fallback to body if no main content found
          const contentElement = mainContent || document.body;

          return {
            title: document.title || '',
            html: contentElement.innerHTML,
            text: contentElement.textContent || '',
            author: document.querySelector('meta[name="author"]')?.getAttribute('content') || '',
            publishedDate: document.querySelector('meta[property="article:published_time"]')?.getAttribute('content') ||
                          document.querySelector('meta[name="date"]')?.getAttribute('content') || '',
            language: document.documentElement.lang || '',
          };
        });

        // Convert HTML to Markdown
        const markdown = this.turndownService.turndown(content.html);
        const cleanText = sanitizeText(content.text);

        return {
          url,
          title: content.title,
          content: cleanText,
          markdown,
          metadata: {
            author: content.author || undefined,
            published_date: content.publishedDate || undefined,
            word_count: cleanText.split(/\s+/).length,
            language: content.language || undefined,
          },
          extracted_at: getCurrentTimestamp(),
        };

      } finally {
        await page.close();
      }
    }, 3, 2000);
  }

  async extractMultiple(urls: string[], maxConcurrent: number = 3): Promise<WebContent[]> {
    const results: WebContent[] = [];
    const chunks = this.chunkArray(urls, maxConcurrent);

    for (const chunk of chunks) {
      const promises = chunk.map(async (url) => {
        try {
          return await this.extractContent(url);
        } catch (error) {
          console.warn(`Failed to extract content from ${url}:`, error);
          return null;
        }
      });

      const chunkResults = await Promise.all(promises);
      results.push(...chunkResults.filter((result): result is WebContent => result !== null));
    }

    return results;
  }

  async searchPageContent(url: string, searchTerms: string[]): Promise<string[]> {
    const content = await this.extractContent(url);
    const matches: string[] = [];

    const sentences = content.content.split(/[.!?]+/);

    for (const term of searchTerms) {
      const termLower = term.toLowerCase();
      
      for (const sentence of sentences) {
        if (sentence.toLowerCase().includes(termLower)) {
          matches.push(sentence.trim());
        }
      }
    }

    return matches;
  }

  async takeScreenshot(url: string, outputPath?: string): Promise<Buffer> {
    await this.initialize();
    const page = await this.createPage();

    try {
      await page.goto(url, { waitUntil: 'networkidle2' });
      
      const screenshotOptions: any = {
        fullPage: true,
        type: 'png',
      };

      if (outputPath) {
        screenshotOptions.path = outputPath;
      }

      const screenshot = await page.screenshot(screenshotOptions);

      return screenshot as Buffer;
    } finally {
      await page.close();
    }
  }

  async checkPageAccessibility(url: string): Promise<{
    accessible: boolean;
    statusCode?: number;
    error?: string;
  }> {
    await this.initialize();
    const page = await this.createPage();

    try {
      const response = await page.goto(url, { 
        waitUntil: 'networkidle2',
        timeout: 15000,
      });

      return {
        accessible: true,
        statusCode: response?.status() || undefined,
      };
    } catch (error) {
      return {
        accessible: false,
        error: (error as Error).message,
      };
    } finally {
      await page.close();
    }
  }

  private async createPage(): Promise<Page> {
    if (!this.browser) {
      throw new BrowsingError('Browser not initialized');
    }

    const page = await this.browser.newPage();

    // Set user agent if provided
    if (this.config.userAgent) {
      await page.setUserAgent(this.config.userAgent);
    }

    // Set viewport
    await page.setViewport({ width: 1920, height: 1080 });

    // Block unnecessary resources to speed up loading
    await page.setRequestInterception(true);
    page.on('request', (req) => {
      const resourceType = req.resourceType();
      if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
        req.abort();
      } else {
        req.continue();
      }
    });

    return page;
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}
