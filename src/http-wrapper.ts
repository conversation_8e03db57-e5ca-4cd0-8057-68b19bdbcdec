#!/usr/bin/env node

/**
 * HTTP wrapper for Deep Research MCP Server
 * Provides REST API endpoints for Clara AI integration
 */

import express from 'express';
import cors from 'cors';
import rateLimit from 'express-rate-limit';
import { StorageManager } from './storage.js';
import { SearchManager } from './searcher.js';
import { BrowserManager } from './browser.js';
import { ResearchPlanner } from './planner.js';
import { ResearchReasoner } from './reasoner.js';
import { ResearchSynthesizer } from './synthesizer.js';
import {
  DeepResearchToolParams,
  GetResearchStatusParams,
  ContinueResearchParams,
  Config,
  ConfigSchema,
  ResearchTask,
  Finding
} from './types.js';
import {
  generateId,
  getCurrentTimestamp,
  calculateRelevanceScore,
  validateConfig,
} from './utils.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

class HttpWrapper {
  private app: express.Application;
  private storage: StorageManager;
  private searcher: SearchManager;
  private browser: BrowserManager;
  private planner: ResearchPlanner;
  private reasoner: ResearchReasoner;
  private synthesizer: ResearchSynthesizer;
  private config: Config;
  private port: number;
  private activeTasks = new Map<string, Promise<void>>();

  constructor(port: number = 3001) {
    this.app = express();
    this.port = port;
    this.config = this.loadConfig();
    validateConfig(this.config);

    // Initialize components
    this.storage = new StorageManager(this.config.storage.database_path);
    this.searcher = new SearchManager(this.config.search);
    this.browser = new BrowserManager(this.config.browser);
    this.planner = new ResearchPlanner(this.config.ai);
    this.reasoner = new ResearchReasoner(this.config.ai);
    this.synthesizer = new ResearchSynthesizer(this.config.ai);

    this.setupMiddleware();
    this.setupRoutes();
  }

  private loadConfig(): Config {
    const config = {
      ai: {
        provider: process.env.AI_PROVIDER || 'openai',
        model: process.env.AI_MODEL || 'gpt-4',
        api_key: process.env.AI_API_KEY,
        base_url: process.env.AI_BASE_URL,
        max_tokens: parseInt(process.env.AI_MAX_TOKENS || '4000'),
        temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),
      },
      search: {
        provider: process.env.SEARCH_PROVIDER || 'serper',
        api_key: process.env.SEARCH_API_KEY,
        max_results: parseInt(process.env.SEARCH_MAX_RESULTS || '10'),
        safe_search: process.env.SEARCH_SAFE_SEARCH !== 'false',
      },
      browser: {
        headless: process.env.BROWSER_HEADLESS !== 'false',
        timeout: parseInt(process.env.BROWSER_TIMEOUT || '30000'),
        user_agent: process.env.BROWSER_USER_AGENT,
      },
      storage: {
        database_path: process.env.DATABASE_PATH || './research.db',
      },
      limits: {
        max_concurrent_searches: parseInt(process.env.MAX_CONCURRENT_SEARCHES || '3'),
        max_pages_per_task: parseInt(process.env.MAX_PAGES_PER_TASK || '50'),
        max_task_duration_minutes: parseInt(process.env.MAX_TASK_DURATION_MINUTES || '30'),
      },
    };

    return ConfigSchema.parse(config);
  }

  private setupMiddleware(): void {
    // CORS for Clara AI
    this.app.use(cors({
      origin: ['http://localhost:3000', 'https://clara.ai', 'https://*.clara.ai'],
      credentials: true
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP'
    });
    this.app.use(limiter);

    // JSON parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Logging
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  private setupRoutes(): void {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        service: 'Deep Research MCP Server HTTP Wrapper'
      });
    });

    // API info
    this.app.get('/api', (req, res) => {
      res.json({
        name: 'Deep Research MCP Server API',
        version: '1.0.0',
        endpoints: {
          'POST /api/research': 'Start deep research',
          'GET /api/research/:taskId': 'Get research status',
          'POST /api/research/:taskId/continue': 'Continue research',
          'GET /api/tasks': 'List all tasks',
          'GET /health': 'Health check'
        },
        documentation: 'See README.md for detailed usage'
      });
    });

    // Start deep research
    this.app.post('/api/research', async (req, res) => {
      try {
        const params: DeepResearchToolParams = {
          query: req.body.query,
          max_pages: req.body.max_pages || 20,
          focus_areas: req.body.focus_areas,
          exclude_domains: req.body.exclude_domains,
          language: req.body.language || 'en'
        };

        if (!params.query) {
          return res.status(400).json({
            error: 'Query is required',
            example: { query: 'latest developments in quantum computing 2024' }
          });
        }

        const result = await this.handleDeepResearch(params);
        
        // Extract task ID from the response
        const taskIdMatch = result.content[0]?.text?.match(/Task ID:\*\* ([a-f0-9-]+)/);
        const taskId = taskIdMatch ? taskIdMatch[1] : null;

        res.json({
          success: true,
          task_id: taskId,
          message: result.content[0]?.text || 'Research started',
          status: 'started'
        });

      } catch (error) {
        console.error('Research start error:', error);
        res.status(500).json({
          error: 'Failed to start research',
          details: (error as Error).message
        });
      }
    });

    // Get research status
    this.app.get('/api/research/:taskId', async (req, res) => {
      try {
        const params: GetResearchStatusParams = {
          task_id: req.params.taskId
        };

        const result = await this.handleGetResearchStatus(params);
        
        // Parse the status from the response text
        const statusText = result.content[0]?.text || '';
        const statusMatch = statusText.match(/\*\*Status:\*\* ([^\n]+)/);
        const progressMatch = statusText.match(/\*\*Progress:\*\* (\d+)%/);
        const findingsMatch = statusText.match(/\*\*Findings Collected:\*\* (\d+)/);

        res.json({
          success: true,
          task_id: req.params.taskId,
          status: statusMatch ? statusMatch[1].toLowerCase() : 'unknown',
          progress: progressMatch ? parseInt(progressMatch[1]) : 0,
          findings_count: findingsMatch ? parseInt(findingsMatch[1]) : 0,
          full_response: statusText
        });

      } catch (error) {
        console.error('Status check error:', error);
        res.status(500).json({
          error: 'Failed to get research status',
          details: (error as Error).message
        });
      }
    });

    // Continue research
    this.app.post('/api/research/:taskId/continue', async (req, res) => {
      try {
        const params: ContinueResearchParams = {
          task_id: req.params.taskId,
          additional_instructions: req.body.additional_instructions
        };

        const result = await this.handleContinueResearch(params);

        res.json({
          success: true,
          task_id: req.params.taskId,
          message: result.content[0]?.text || 'Research continued',
          status: 'continued'
        });

      } catch (error) {
        console.error('Continue research error:', error);
        res.status(500).json({
          error: 'Failed to continue research',
          details: (error as Error).message
        });
      }
    });

    // List all tasks (bonus endpoint)
    this.app.get('/api/tasks', async (req, res) => {
      try {
        // This would require adding a method to list tasks in the main server
        res.json({
          message: 'Task listing not yet implemented',
          suggestion: 'Use specific task IDs to check status'
        });
      } catch (error) {
        res.status(500).json({
          error: 'Failed to list tasks',
          details: (error as Error).message
        });
      }
    });

    // Error handling
    this.app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
      console.error('Unhandled error:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    });

    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Endpoint not found',
        available_endpoints: [
          'GET /health',
          'GET /api',
          'POST /api/research',
          'GET /api/research/:taskId',
          'POST /api/research/:taskId/continue'
        ]
      });
    });
  }

  async start(): Promise<void> {
    try {
      await this.server.initialize();
      
      this.app.listen(this.port, () => {
        console.log(`🌐 Deep Research HTTP API running on http://localhost:${this.port}`);
        console.log(`📚 API documentation: http://localhost:${this.port}/api`);
        console.log(`❤️  Health check: http://localhost:${this.port}/health`);
        console.log('');
        console.log('Example usage:');
        console.log(`curl -X POST http://localhost:${this.port}/api/research \\`);
        console.log(`  -H "Content-Type: application/json" \\`);
        console.log(`  -d '{"query": "latest AI developments 2024", "max_pages": 15}'`);
      });
    } catch (error) {
      console.error('Failed to start HTTP wrapper:', error);
      process.exit(1);
    }
  }
}

// Start server if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const port = parseInt(process.env.HTTP_PORT || '3001');
  const wrapper = new HttpWrapper(port);
  wrapper.start();
}

export { HttpWrapper };
