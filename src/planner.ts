import { ResearchPlan, AIConfig, AIError } from './types.js';
import { generateId } from './utils.js';
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';

export class ResearchPlanner {
  private aiConfig: AIConfig;
  private openai?: OpenAI;
  private anthropic?: Anthropic;

  constructor(aiConfig: AIConfig) {
    this.aiConfig = aiConfig;
    this.initializeAI();
  }

  private initializeAI(): void {
    switch (this.aiConfig.provider) {
      case 'openai':
        if (!this.aiConfig.api_key) {
          throw new AIError('OpenAI API key not provided');
        }
        this.openai = new OpenAI({
          apiKey: this.aiConfig.api_key,
          baseURL: this.aiConfig.base_url,
        });
        break;
      case 'anthropic':
        if (!this.aiConfig.api_key) {
          throw new AIError('Anthropic API key not provided');
        }
        this.anthropic = new Anthropic({
          apiKey: this.aiConfig.api_key,
        });
        break;
      case 'local':
      case 'ollama':
        // For local/Ollama, use OpenAI-compatible API
        this.openai = new OpenAI({
          apiKey: this.aiConfig.api_key || 'not-required',
          baseURL: this.aiConfig.base_url || 'http://localhost:11434/v1',
        });
        break;
      default:
        throw new AIError(`Unsupported AI provider: ${this.aiConfig.provider}`);
    }
  }

  async createResearchPlan(query: string, focusAreas?: string[]): Promise<ResearchPlan[]> {
    const prompt = this.buildPlanningPrompt(query, focusAreas);
    
    try {
      const response = await this.callAI(prompt);
      return this.parsePlanResponse(response);
    } catch (error) {
      throw new AIError(`Failed to create research plan: ${(error as Error).message}`);
    }
  }

  private buildPlanningPrompt(query: string, focusAreas?: string[]): string {
    let prompt = `You are an expert research planner. Create a comprehensive research plan for the following query:

QUERY: "${query}"`;

    if (focusAreas && focusAreas.length > 0) {
      prompt += `\n\nFOCUS AREAS: ${focusAreas.join(', ')}`;
    }

    prompt += `

Create a detailed research plan with 5-8 specific subtasks. Each subtask should:
1. Be specific and actionable
2. Focus on a particular aspect of the query
3. Be suitable for web search and content analysis
4. Build upon previous subtasks when appropriate

For each subtask, provide:
- A clear description of what to research
- Priority level (1-5, where 1 is highest priority)
- Any dependencies on other subtasks

Format your response as a JSON array of objects with this structure:
{
  "description": "Specific research task description",
  "priority": 1-5,
  "dependencies": ["id1", "id2"] // IDs of tasks that should be completed first
}

Example response:
[
  {
    "description": "Research the fundamental concepts and definitions related to [topic]",
    "priority": 1,
    "dependencies": []
  },
  {
    "description": "Analyze current market trends and statistics for [topic]",
    "priority": 2,
    "dependencies": []
  },
  {
    "description": "Investigate key players and organizations in the [topic] space",
    "priority": 2,
    "dependencies": []
  },
  {
    "description": "Examine recent developments and news about [topic]",
    "priority": 3,
    "dependencies": []
  },
  {
    "description": "Compare different approaches or solutions related to [topic]",
    "priority": 3,
    "dependencies": ["task1", "task2"]
  }
]

Respond with ONLY the JSON array, no additional text.`;

    return prompt;
  }

  private async callAI(prompt: string): Promise<string> {
    switch (this.aiConfig.provider) {
      case 'openai':
      case 'local':
      case 'ollama':
        return this.callOpenAI(prompt);
      case 'anthropic':
        return this.callAnthropic(prompt);
      default:
        throw new AIError(`Unsupported AI provider: ${this.aiConfig.provider}`);
    }
  }

  private async callOpenAI(prompt: string): Promise<string> {
    if (!this.openai) {
      throw new AIError('OpenAI client not initialized');
    }

    const response = await this.openai.chat.completions.create({
      model: this.aiConfig.model,
      messages: [{ role: 'user', content: prompt }],
      max_tokens: this.aiConfig.max_tokens,
      temperature: this.aiConfig.temperature,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new AIError('No response from OpenAI');
    }

    return content;
  }

  private async callAnthropic(prompt: string): Promise<string> {
    if (!this.anthropic) {
      throw new AIError('Anthropic client not initialized');
    }

    const response = await this.anthropic.messages.create({
      model: this.aiConfig.model,
      max_tokens: this.aiConfig.max_tokens,
      temperature: this.aiConfig.temperature,
      messages: [{ role: 'user', content: prompt }],
    });

    const content = response.content[0];
    if (content && content.type === 'text') {
      return content.text;
    }

    throw new AIError('Unexpected response type from Anthropic');
  }

  private parsePlanResponse(response: string): ResearchPlan[] {
    try {
      // Extract JSON from response (in case there's extra text)
      const jsonMatch = response.match(/\[[\s\S]*\]/);
      const jsonStr = jsonMatch ? jsonMatch[0] : response;
      
      const planData = JSON.parse(jsonStr);
      
      if (!Array.isArray(planData)) {
        throw new Error('Response is not an array');
      }

      return planData.map((item, index) => ({
        id: generateId(),
        description: item.description || `Research task ${index + 1}`,
        status: 'pending' as const,
        priority: item.priority || 3,
        dependencies: item.dependencies || [],
      }));
    } catch (error) {
      // Fallback: create a basic plan if parsing fails
      console.warn('Failed to parse AI response, creating fallback plan:', error);
      return this.createFallbackPlan();
    }
  }

  private createFallbackPlan(): ResearchPlan[] {
    return [
      {
        id: generateId(),
        description: 'Research basic concepts and definitions',
        status: 'pending',
        priority: 1,
        dependencies: [],
      },
      {
        id: generateId(),
        description: 'Find current statistics and data',
        status: 'pending',
        priority: 2,
        dependencies: [],
      },
      {
        id: generateId(),
        description: 'Identify key organizations and experts',
        status: 'pending',
        priority: 2,
        dependencies: [],
      },
      {
        id: generateId(),
        description: 'Research recent developments and trends',
        status: 'pending',
        priority: 3,
        dependencies: [],
      },
      {
        id: generateId(),
        description: 'Analyze different approaches and solutions',
        status: 'pending',
        priority: 3,
        dependencies: [],
      },
    ];
  }

  async refinePlan(
    currentPlan: ResearchPlan[],
    findings: string[],
    additionalInstructions?: string
  ): Promise<ResearchPlan[]> {
    const prompt = `You are refining a research plan based on current findings. 

CURRENT PLAN:
${currentPlan.map(p => `- ${p.description} (Status: ${p.status}, Priority: ${p.priority})`).join('\n')}

FINDINGS SO FAR:
${findings.slice(0, 5).map(f => `- ${f.substring(0, 200)}...`).join('\n')}

${additionalInstructions ? `ADDITIONAL INSTRUCTIONS: ${additionalInstructions}` : ''}

Based on the findings, suggest modifications to the research plan:
1. Which completed tasks can be marked as done?
2. Should any pending tasks be modified or reprioritized?
3. Are there new tasks that should be added?
4. Should any tasks be removed as no longer relevant?

Respond with a JSON array of the updated plan using the same format as before.`;

    try {
      const response = await this.callAI(prompt);
      const refinedPlan = this.parsePlanResponse(response);
      
      // Preserve IDs for existing tasks where possible
      return this.mergePlans(currentPlan, refinedPlan);
    } catch (error) {
      console.warn('Failed to refine plan, keeping current plan:', error);
      return currentPlan;
    }
  }

  private mergePlans(currentPlan: ResearchPlan[], refinedPlan: ResearchPlan[]): ResearchPlan[] {
    const merged: ResearchPlan[] = [];
    
    // Keep existing tasks that are still relevant
    for (const currentTask of currentPlan) {
      const currentTaskFirstWord = currentTask.description.toLowerCase().split(' ')[0];
      const matchingRefined = refinedPlan.find(r =>
        r.description.toLowerCase().includes(currentTaskFirstWord)
      );
      
      if (matchingRefined) {
        merged.push({
          ...currentTask,
          description: matchingRefined.description,
          priority: matchingRefined.priority,
          dependencies: matchingRefined.dependencies,
        });
      } else if (currentTask.status !== 'completed') {
        merged.push(currentTask);
      }
    }
    
    // Add new tasks from refined plan
    for (const refinedTask of refinedPlan) {
      const refinedTaskFirstWord = refinedTask.description.toLowerCase().split(' ')[0];
      const exists = merged.some(m =>
        m.description.toLowerCase().includes(refinedTaskFirstWord)
      );
      
      if (!exists) {
        merged.push(refinedTask);
      }
    }
    
    return merged;
  }
}
