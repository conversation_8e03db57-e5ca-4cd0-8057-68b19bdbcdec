import { z } from 'zod';
// Research Task Types
export const ResearchTaskSchema = z.object({
    id: z.string(),
    query: z.string(),
    status: z.enum(['planning', 'searching', 'reasoning', 'synthesizing', 'completed', 'failed']),
    plan: z.array(z.object({
        id: z.string(),
        description: z.string(),
        status: z.enum(['pending', 'in_progress', 'completed', 'failed']),
        priority: z.number(),
        dependencies: z.array(z.string()).optional(),
    })),
    findings: z.array(z.object({
        id: z.string(),
        source: z.string(),
        url: z.string(),
        title: z.string(),
        content: z.string(),
        relevance_score: z.number(),
        timestamp: z.string(),
        task_id: z.string(),
    })),
    reasoning_steps: z.array(z.object({
        id: z.string(),
        task_id: z.string(),
        step: z.string(),
        reasoning: z.string(),
        decision: z.string(),
        timestamp: z.string(),
    })),
    report: z.string().optional(),
    created_at: z.string(),
    updated_at: z.string(),
});
// Search Types
export const SearchResultSchema = z.object({
    title: z.string(),
    url: z.string(),
    snippet: z.string(),
    source: z.string(),
    timestamp: z.string(),
});
// Web Content Types
export const WebContentSchema = z.object({
    url: z.string(),
    title: z.string(),
    content: z.string(),
    markdown: z.string(),
    metadata: z.object({
        author: z.string().optional(),
        published_date: z.string().optional(),
        word_count: z.number(),
        language: z.string().optional(),
    }),
    extracted_at: z.string(),
});
// AI Provider Types
export const AIProviderSchema = z.enum(['openai', 'anthropic', 'local', 'ollama']);
export const AIConfigSchema = z.object({
    provider: AIProviderSchema,
    model: z.string(),
    api_key: z.string().optional(),
    base_url: z.string().optional(),
    max_tokens: z.number().default(4000),
    temperature: z.number().default(0.7),
});
// Search Provider Types
export const SearchProviderSchema = z.enum(['google', 'bing', 'duckduckgo', 'serper']);
export const SearchConfigSchema = z.object({
    provider: SearchProviderSchema,
    api_key: z.string().optional(),
    max_results: z.number().default(10),
    safe_search: z.boolean().default(true),
});
// Configuration Types
export const ConfigSchema = z.object({
    ai: AIConfigSchema,
    search: SearchConfigSchema,
    browser: z.object({
        headless: z.boolean().default(true),
        timeout: z.number().default(30000),
        user_agent: z.string().optional(),
    }),
    storage: z.object({
        database_path: z.string().default('./research.db'),
    }),
    limits: z.object({
        max_concurrent_searches: z.number().default(3),
        max_pages_per_task: z.number().default(50),
        max_task_duration_minutes: z.number().default(30),
    }),
});
// Error Types
export class ResearchError extends Error {
    code;
    details;
    constructor(message, code, details) {
        super(message);
        this.code = code;
        this.details = details;
        this.name = 'ResearchError';
    }
}
export class SearchError extends ResearchError {
    constructor(message, details) {
        super(message, 'SEARCH_ERROR', details);
    }
}
export class BrowsingError extends ResearchError {
    constructor(message, details) {
        super(message, 'BROWSING_ERROR', details);
    }
}
export class AIError extends ResearchError {
    constructor(message, details) {
        super(message, 'AI_ERROR', details);
    }
}
//# sourceMappingURL=types.js.map