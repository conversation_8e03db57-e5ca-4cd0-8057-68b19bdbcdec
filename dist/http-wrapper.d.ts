#!/usr/bin/env node
/**
 * HTTP wrapper for Deep Research MCP Server
 * Provides REST API endpoints for Clara AI integration
 */
declare class HttpWrapper {
    private app;
    private storage;
    private searcher;
    private browser;
    private planner;
    private reasoner;
    private synthesizer;
    private config;
    private port;
    private activeTasks;
    constructor(port?: number);
    private loadConfig;
    private setupMiddleware;
    private setupRoutes;
    start(): Promise<void>;
}
export { HttpWrapper };
//# sourceMappingURL=http-wrapper.d.ts.map