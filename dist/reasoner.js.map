{"version": 3, "file": "reasoner.js", "sourceRoot": "", "sources": ["../src/reasoner.ts"], "names": [], "mappings": "AAAA,OAAO,EAAkD,OAAO,EAAE,MAAM,YAAY,CAAC;AACrF,OAAO,EAAE,UAAU,EAAE,mBAAmB,EAA2B,MAAM,YAAY,CAAC;AACtF,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,SAAS,MAAM,mBAAmB,CAAC;AAE1C,MAAM,OAAO,gBAAgB;IACnB,QAAQ,CAAW;IACnB,MAAM,CAAU;IAChB,SAAS,CAAa;IAE9B,YAAY,QAAkB;QAC5B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEO,YAAY;QAClB,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC/B,KAAK,QAAQ;gBACX,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;oBAC3B,MAAM,IAAI,OAAO,CAAC,6BAA6B,CAAC,CAAC;gBACnD,CAAC;gBACD,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;oBACvB,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;oBAC7B,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ;iBAChC,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;oBAC3B,MAAM,IAAI,OAAO,CAAC,gCAAgC,CAAC,CAAC;gBACtD,CAAC;gBACD,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC;oBAC7B,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;iBAC9B,CAAC,CAAC;gBACH,MAAM;YACR;gBACE,MAAM,IAAI,OAAO,CAAC,4BAA4B,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,IAAkB,EAClB,WAAsB;QAOtB,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAE3D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,OAAO,CAAC,+BAAgC,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,IAAkB,EAAE,WAAsB;QACpE,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;QACvE,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;QACnE,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,CAAC;QAEvD,OAAO;;mBAEQ,IAAI,CAAC,KAAK;;;EAG3B,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;EAGxD,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,WAAW,eAAe,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;oBAE9D,WAAW,CAAC,MAAM;EACpC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;UACxB,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,GAAG;SACnB,CAAC,CAAC,KAAK;WACL,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;aACzB,CAAC,CAAC,eAAe;CAC7B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;EAGX,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,SAAS,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uDAiC1C,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,MAAM,CAAC,MAAc;QACjC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC/B,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACjC,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YACpC;gBACE,MAAM,IAAI,OAAO,CAAC,4BAA4B,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,MAAc;QACrC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,OAAO,CAAC,+BAA+B,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACzD,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK;YAC1B,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;YAC7C,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;YACpC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW;SACvC,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;QACtD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,MAAc;QACxC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,IAAI,OAAO,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;YACpD,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK;YAC1B,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;YACpC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW;YACtC,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;SAC9C,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAC5B,MAAM,IAAI,OAAO,CAAC,yCAAyC,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,CAAC;IACtB,CAAC;IAEO,qBAAqB,CAAC,QAAgB,EAAE,MAAc;QAM5D,IAAI,CAAC;YACH,6BAA6B;YAC7B,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAEpD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAErC,MAAM,SAAS,GAAkB;gBAC/B,EAAE,EAAE,UAAU,EAAE;gBAChB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE,8BAA8B;gBACpC,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,oBAAoB;gBACrD,QAAQ,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,sBAAsB;gBAChF,SAAS,EAAE,mBAAmB,EAAE;aACjC,CAAC;YAEF,OAAO;gBACL,SAAS;gBACT,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,EAAE;gBACvC,cAAc,EAAE,QAAQ,CAAC,cAAc,IAAI,IAAI;gBAC/C,kBAAkB,EAAE,QAAQ,CAAC,kBAAkB,IAAI,EAAE;aACtD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;YAE1E,qBAAqB;YACrB,MAAM,SAAS,GAAkB;gBAC/B,EAAE,EAAE,UAAU,EAAE;gBAChB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE,mBAAmB;gBACzB,SAAS,EAAE,+DAA+D;gBAC1E,QAAQ,EAAE,0CAA0C;gBACpD,SAAS,EAAE,mBAAmB,EAAE;aACjC,CAAC;YAEF,OAAO;gBACL,SAAS;gBACT,WAAW,EAAE,CAAC,0CAA0C,CAAC;gBACzD,cAAc,EAAE,IAAI;gBACpB,kBAAkB,EAAE,CAAC,mCAAmC,CAAC;aAC1D,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,IAAkB;QAM3C,MAAM,MAAM,GAAG;;UAET,IAAI,CAAC,KAAK;;sBAEE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;;;oBAG5E,IAAI,CAAC,QAAQ,CAAC,MAAM;uBACjB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClI,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI;;;EAGhE,IAAI,CAAC,QAAQ;aACZ,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,eAAe,CAAC;aACrD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;aACX,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,eAAe,MAAM,CAAC;aAClD,IAAI,CAAC,IAAI,CAAC;;;;;;;;;;EAUX,CAAC;QAEC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YACpD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAEvC,OAAO;gBACL,UAAU,EAAE,UAAU,CAAC,UAAU,IAAI,KAAK;gBAC1C,iBAAiB,EAAE,UAAU,CAAC,iBAAiB,IAAI,CAAC;gBACpD,SAAS,EAAE,UAAU,CAAC,SAAS,IAAI,+BAA+B;gBAClE,eAAe,EAAE,UAAU,CAAC,eAAe,IAAI,EAAE;aAClD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAExD,8CAA8C;YAC9C,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM,CAAC;YAC9E,MAAM,eAAe,GAAG,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC7C,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1F,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAClC,CAAC,eAAe,GAAG,CAAC,CAAC;gBACrB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrB,CAAC,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CACxB,CAAC;YAEF,OAAO;gBACL,UAAU,EAAE,iBAAiB,IAAI,CAAC;gBAClC,iBAAiB;gBACjB,SAAS,EAAE,wBAAwB,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,qBAAqB,IAAI,CAAC,QAAQ,CAAC,MAAM,oCAAoC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;gBAC9K,eAAe,EAAE,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,mCAAmC,EAAE,8BAA8B,CAAC,CAAC,CAAC,CAAC,EAAE;aACpH,CAAC;QACJ,CAAC;IACH,CAAC;CACF"}