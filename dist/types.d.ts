import { z } from 'zod';
export declare const ResearchTaskSchema: z.ZodObject<{
    id: z.ZodString;
    query: z.ZodString;
    status: z.<PERSON><PERSON><["planning", "searching", "reasoning", "synthesizing", "completed", "failed"]>;
    plan: z.<PERSON><z.ZodObject<{
        id: z.ZodString;
        description: z.ZodString;
        status: z.ZodE<PERSON><["pending", "in_progress", "completed", "failed"]>;
        priority: z.Zod<PERSON>;
        dependencies: z.<PERSON>od<PERSON>ptional<z.<PERSON>od<PERSON>rray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        status: "completed" | "failed" | "pending" | "in_progress";
        description: string;
        priority: number;
        dependencies?: string[] | undefined;
    }, {
        id: string;
        status: "completed" | "failed" | "pending" | "in_progress";
        description: string;
        priority: number;
        dependencies?: string[] | undefined;
    }>, "many">;
    findings: z.<PERSON><z.ZodObject<{
        id: z.ZodString;
        source: z.ZodString;
        url: z.ZodString;
        title: z.ZodString;
        content: z.ZodString;
        relevance_score: z.ZodNumber;
        timestamp: z.ZodString;
        task_id: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        id: string;
        source: string;
        url: string;
        title: string;
        content: string;
        relevance_score: number;
        timestamp: string;
        task_id: string;
    }, {
        id: string;
        source: string;
        url: string;
        title: string;
        content: string;
        relevance_score: number;
        timestamp: string;
        task_id: string;
    }>, "many">;
    reasoning_steps: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        step: z.ZodString;
        reasoning: z.ZodString;
        decision: z.ZodString;
        timestamp: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        id: string;
        reasoning: string;
        timestamp: string;
        step: string;
        decision: string;
    }, {
        id: string;
        reasoning: string;
        timestamp: string;
        step: string;
        decision: string;
    }>, "many">;
    report: z.ZodOptional<z.ZodString>;
    created_at: z.ZodString;
    updated_at: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    query: string;
    status: "planning" | "searching" | "reasoning" | "synthesizing" | "completed" | "failed";
    plan: {
        id: string;
        status: "completed" | "failed" | "pending" | "in_progress";
        description: string;
        priority: number;
        dependencies?: string[] | undefined;
    }[];
    findings: {
        id: string;
        source: string;
        url: string;
        title: string;
        content: string;
        relevance_score: number;
        timestamp: string;
        task_id: string;
    }[];
    reasoning_steps: {
        id: string;
        reasoning: string;
        timestamp: string;
        step: string;
        decision: string;
    }[];
    created_at: string;
    updated_at: string;
    report?: string | undefined;
}, {
    id: string;
    query: string;
    status: "planning" | "searching" | "reasoning" | "synthesizing" | "completed" | "failed";
    plan: {
        id: string;
        status: "completed" | "failed" | "pending" | "in_progress";
        description: string;
        priority: number;
        dependencies?: string[] | undefined;
    }[];
    findings: {
        id: string;
        source: string;
        url: string;
        title: string;
        content: string;
        relevance_score: number;
        timestamp: string;
        task_id: string;
    }[];
    reasoning_steps: {
        id: string;
        reasoning: string;
        timestamp: string;
        step: string;
        decision: string;
    }[];
    created_at: string;
    updated_at: string;
    report?: string | undefined;
}>;
export type ResearchTask = z.infer<typeof ResearchTaskSchema>;
export type ResearchPlan = ResearchTask['plan'][0];
export type Finding = ResearchTask['findings'][0];
export type ReasoningStep = ResearchTask['reasoning_steps'][0];
export declare const SearchResultSchema: z.ZodObject<{
    title: z.ZodString;
    url: z.ZodString;
    snippet: z.ZodString;
    source: z.ZodString;
    timestamp: z.ZodString;
}, "strip", z.ZodTypeAny, {
    source: string;
    url: string;
    title: string;
    timestamp: string;
    snippet: string;
}, {
    source: string;
    url: string;
    title: string;
    timestamp: string;
    snippet: string;
}>;
export type SearchResult = z.infer<typeof SearchResultSchema>;
export declare const WebContentSchema: z.ZodObject<{
    url: z.ZodString;
    title: z.ZodString;
    content: z.ZodString;
    markdown: z.ZodString;
    metadata: z.ZodObject<{
        author: z.ZodOptional<z.ZodString>;
        published_date: z.ZodOptional<z.ZodString>;
        word_count: z.ZodNumber;
        language: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        word_count: number;
        author?: string | undefined;
        published_date?: string | undefined;
        language?: string | undefined;
    }, {
        word_count: number;
        author?: string | undefined;
        published_date?: string | undefined;
        language?: string | undefined;
    }>;
    extracted_at: z.ZodString;
}, "strip", z.ZodTypeAny, {
    url: string;
    title: string;
    content: string;
    markdown: string;
    metadata: {
        word_count: number;
        author?: string | undefined;
        published_date?: string | undefined;
        language?: string | undefined;
    };
    extracted_at: string;
}, {
    url: string;
    title: string;
    content: string;
    markdown: string;
    metadata: {
        word_count: number;
        author?: string | undefined;
        published_date?: string | undefined;
        language?: string | undefined;
    };
    extracted_at: string;
}>;
export type WebContent = z.infer<typeof WebContentSchema>;
export declare const AIProviderSchema: z.ZodEnum<["openai", "anthropic", "local"]>;
export type AIProvider = z.infer<typeof AIProviderSchema>;
export declare const AIConfigSchema: z.ZodObject<{
    provider: z.ZodEnum<["openai", "anthropic", "local"]>;
    model: z.ZodString;
    api_key: z.ZodOptional<z.ZodString>;
    base_url: z.ZodOptional<z.ZodString>;
    max_tokens: z.ZodDefault<z.ZodNumber>;
    temperature: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    provider: "openai" | "anthropic" | "local";
    model: string;
    max_tokens: number;
    temperature: number;
    api_key?: string | undefined;
    base_url?: string | undefined;
}, {
    provider: "openai" | "anthropic" | "local";
    model: string;
    api_key?: string | undefined;
    base_url?: string | undefined;
    max_tokens?: number | undefined;
    temperature?: number | undefined;
}>;
export type AIConfig = z.infer<typeof AIConfigSchema>;
export declare const SearchProviderSchema: z.ZodEnum<["google", "bing", "duckduckgo", "serper"]>;
export type SearchProvider = z.infer<typeof SearchProviderSchema>;
export declare const SearchConfigSchema: z.ZodObject<{
    provider: z.ZodEnum<["google", "bing", "duckduckgo", "serper"]>;
    api_key: z.ZodOptional<z.ZodString>;
    max_results: z.ZodDefault<z.ZodNumber>;
    safe_search: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    provider: "google" | "bing" | "duckduckgo" | "serper";
    max_results: number;
    safe_search: boolean;
    api_key?: string | undefined;
}, {
    provider: "google" | "bing" | "duckduckgo" | "serper";
    api_key?: string | undefined;
    max_results?: number | undefined;
    safe_search?: boolean | undefined;
}>;
export type SearchConfig = z.infer<typeof SearchConfigSchema>;
export declare const ConfigSchema: z.ZodObject<{
    ai: z.ZodObject<{
        provider: z.ZodEnum<["openai", "anthropic", "local"]>;
        model: z.ZodString;
        api_key: z.ZodOptional<z.ZodString>;
        base_url: z.ZodOptional<z.ZodString>;
        max_tokens: z.ZodDefault<z.ZodNumber>;
        temperature: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        provider: "openai" | "anthropic" | "local";
        model: string;
        max_tokens: number;
        temperature: number;
        api_key?: string | undefined;
        base_url?: string | undefined;
    }, {
        provider: "openai" | "anthropic" | "local";
        model: string;
        api_key?: string | undefined;
        base_url?: string | undefined;
        max_tokens?: number | undefined;
        temperature?: number | undefined;
    }>;
    search: z.ZodObject<{
        provider: z.ZodEnum<["google", "bing", "duckduckgo", "serper"]>;
        api_key: z.ZodOptional<z.ZodString>;
        max_results: z.ZodDefault<z.ZodNumber>;
        safe_search: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        provider: "google" | "bing" | "duckduckgo" | "serper";
        max_results: number;
        safe_search: boolean;
        api_key?: string | undefined;
    }, {
        provider: "google" | "bing" | "duckduckgo" | "serper";
        api_key?: string | undefined;
        max_results?: number | undefined;
        safe_search?: boolean | undefined;
    }>;
    browser: z.ZodObject<{
        headless: z.ZodDefault<z.ZodBoolean>;
        timeout: z.ZodDefault<z.ZodNumber>;
        user_agent: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        headless: boolean;
        timeout: number;
        user_agent?: string | undefined;
    }, {
        headless?: boolean | undefined;
        timeout?: number | undefined;
        user_agent?: string | undefined;
    }>;
    storage: z.ZodObject<{
        database_path: z.ZodDefault<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        database_path: string;
    }, {
        database_path?: string | undefined;
    }>;
    limits: z.ZodObject<{
        max_concurrent_searches: z.ZodDefault<z.ZodNumber>;
        max_pages_per_task: z.ZodDefault<z.ZodNumber>;
        max_task_duration_minutes: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        max_concurrent_searches: number;
        max_pages_per_task: number;
        max_task_duration_minutes: number;
    }, {
        max_concurrent_searches?: number | undefined;
        max_pages_per_task?: number | undefined;
        max_task_duration_minutes?: number | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    ai: {
        provider: "openai" | "anthropic" | "local";
        model: string;
        max_tokens: number;
        temperature: number;
        api_key?: string | undefined;
        base_url?: string | undefined;
    };
    search: {
        provider: "google" | "bing" | "duckduckgo" | "serper";
        max_results: number;
        safe_search: boolean;
        api_key?: string | undefined;
    };
    browser: {
        headless: boolean;
        timeout: number;
        user_agent?: string | undefined;
    };
    storage: {
        database_path: string;
    };
    limits: {
        max_concurrent_searches: number;
        max_pages_per_task: number;
        max_task_duration_minutes: number;
    };
}, {
    ai: {
        provider: "openai" | "anthropic" | "local";
        model: string;
        api_key?: string | undefined;
        base_url?: string | undefined;
        max_tokens?: number | undefined;
        temperature?: number | undefined;
    };
    search: {
        provider: "google" | "bing" | "duckduckgo" | "serper";
        api_key?: string | undefined;
        max_results?: number | undefined;
        safe_search?: boolean | undefined;
    };
    browser: {
        headless?: boolean | undefined;
        timeout?: number | undefined;
        user_agent?: string | undefined;
    };
    storage: {
        database_path?: string | undefined;
    };
    limits: {
        max_concurrent_searches?: number | undefined;
        max_pages_per_task?: number | undefined;
        max_task_duration_minutes?: number | undefined;
    };
}>;
export type Config = z.infer<typeof ConfigSchema>;
export interface DeepResearchToolParams {
    query: string;
    max_pages?: number;
    focus_areas?: string[];
    exclude_domains?: string[];
    language?: string;
}
export interface GetResearchStatusParams {
    task_id: string;
}
export interface ContinueResearchParams {
    task_id: string;
    additional_instructions?: string;
}
export declare class ResearchError extends Error {
    code: string;
    details?: Record<string, unknown> | undefined;
    constructor(message: string, code: string, details?: Record<string, unknown> | undefined);
}
export declare class SearchError extends ResearchError {
    constructor(message: string, details?: Record<string, unknown>);
}
export declare class BrowsingError extends ResearchError {
    constructor(message: string, details?: Record<string, unknown>);
}
export declare class AIError extends ResearchError {
    constructor(message: string, details?: Record<string, unknown>);
}
export interface ResearchProgress {
    task_id: string;
    status: ResearchTask['status'];
    progress_percentage: number;
    current_step: string;
    completed_subtasks: number;
    total_subtasks: number;
    findings_count: number;
    estimated_completion_time?: string;
}
//# sourceMappingURL=types.d.ts.map