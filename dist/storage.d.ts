import { ResearchTask, Finding, ReasoningStep, ResearchProgress } from './types.js';
export declare class StorageManager {
    private db;
    private dbRun;
    private dbGet;
    private dbAll;
    constructor(databasePath: string);
    initialize(): Promise<void>;
    private createTables;
    saveTask(task: ResearchTask): Promise<void>;
    getTask(taskId: string): Promise<ResearchTask | null>;
    saveFinding(finding: Finding): Promise<void>;
    getFindings(taskId: string): Promise<Finding[]>;
    saveReasoningStep(step: ReasoningStep): Promise<void>;
    getReasoningSteps(taskId: string): Promise<ReasoningStep[]>;
    updateTaskStatus(taskId: string, status: ResearchTask['status']): Promise<void>;
    getActiveTasks(): Promise<ResearchTask[]>;
    getTaskProgress(taskId: string): Promise<ResearchProgress | null>;
    close(): Promise<void>;
}
//# sourceMappingURL=storage.d.ts.map