{"version": 3, "file": "http-wrapper.js", "sourceRoot": "", "sources": ["../src/http-wrapper.ts"], "names": [], "mappings": ";AAEA;;;GAGG;AAEH,OAAO,OAAO,MAAM,SAAS,CAAC;AAC9B,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,SAAS,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC9C,OAAO,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAC9C,OAAO,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC9C,OAAO,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAC/C,OAAO,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AACjD,OAAO,EAAE,mBAAmB,EAAE,MAAM,kBAAkB,CAAC;AACvD,OAAO,EAKL,YAAY,EAGb,MAAM,YAAY,CAAC;AACpB,OAAO,EAIL,cAAc,GACf,MAAM,YAAY,CAAC;AACpB,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE5B,6BAA6B;AAC7B,MAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,MAAM,WAAW;IACP,GAAG,CAAsB;IACzB,OAAO,CAAiB;IACxB,QAAQ,CAAgB;IACxB,OAAO,CAAiB;IACxB,OAAO,CAAkB;IACzB,QAAQ,CAAmB;IAC3B,WAAW,CAAsB;IACjC,MAAM,CAAS;IACf,IAAI,CAAS;IACb,WAAW,GAAG,IAAI,GAAG,EAAyB,CAAC;IAEvD,YAAY,OAAe,IAAI;QAC7B,IAAI,CAAC,GAAG,GAAG,OAAO,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAChC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE5B,wBAAwB;QACxB,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACrE,IAAI,CAAC,QAAQ,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACrD,IAAI,CAAC,WAAW,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAE3D,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAEO,UAAU;QAChB,MAAM,MAAM,GAAG;YACb,EAAE,EAAE;gBACF,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,QAAQ;gBAC7C,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,OAAO;gBACtC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;gBAC/B,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;gBACjC,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,MAAM,CAAC;gBACzD,WAAW,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK,CAAC;aAC7D;YACD,MAAM,EAAE;gBACN,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,QAAQ;gBACjD,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;gBACnC,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,IAAI,CAAC;gBAC7D,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,KAAK,OAAO;aACxD;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,KAAK,OAAO;gBAClD,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,OAAO,CAAC;gBACzD,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB;aAC3C;YACD,OAAO,EAAE;gBACP,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,eAAe;aAC5D;YACD,MAAM,EAAE;gBACN,uBAAuB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,GAAG,CAAC;gBAC7E,kBAAkB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,IAAI,CAAC;gBACpE,yBAAyB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,IAAI,CAAC;aACnF;SACF,CAAC;QAEF,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAEO,eAAe;QACrB,oBAAoB;QACpB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;YAChB,MAAM,EAAE,CAAC,uBAAuB,EAAE,kBAAkB,EAAE,oBAAoB,CAAC;YAC3E,WAAW,EAAE,IAAI;SAClB,CAAC,CAAC,CAAC;QAEJ,gBAAgB;QAChB,MAAM,OAAO,GAAG,SAAS,CAAC;YACxB,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;YACvC,GAAG,EAAE,GAAG,EAAE,6CAA6C;YACvD,OAAO,EAAE,gCAAgC;SAC1C,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEtB,eAAe;QACf,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAErD,UAAU;QACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;YAC9B,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YACvE,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,WAAW;QACjB,eAAe;QACf,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACnC,GAAG,CAAC,IAAI,CAAC;gBACP,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,uCAAuC;aACjD,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,WAAW;QACX,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAChC,GAAG,CAAC,IAAI,CAAC;gBACP,IAAI,EAAE,8BAA8B;gBACpC,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE;oBACT,oBAAoB,EAAE,qBAAqB;oBAC3C,2BAA2B,EAAE,qBAAqB;oBAClD,qCAAqC,EAAE,mBAAmB;oBAC1D,gBAAgB,EAAE,gBAAgB;oBAClC,aAAa,EAAE,cAAc;iBAC9B;gBACD,aAAa,EAAE,kCAAkC;aAClD,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YAChD,IAAI,CAAC;gBACH,MAAM,MAAM,GAA2B;oBACrC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK;oBACrB,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE;oBACnC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,WAAW;oBACjC,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC,eAAe;oBACzC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI;iBACpC,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;oBAClB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,mBAAmB;wBAC1B,OAAO,EAAE,EAAE,KAAK,EAAE,+CAA+C,EAAE;qBACpE,CAAC,CAAC;gBACL,CAAC;gBAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;gBAErD,oCAAoC;gBACpC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBAChF,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAEnD,GAAG,CAAC,IAAI,CAAC;oBACP,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,MAAM;oBACf,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,kBAAkB;oBACtD,MAAM,EAAE,SAAS;iBAClB,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;gBAC9C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,0BAA0B;oBACjC,OAAO,EAAG,KAAe,CAAC,OAAO;iBAClC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YACvD,IAAI,CAAC;gBACH,MAAM,MAAM,GAA4B;oBACtC,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM;iBAC3B,CAAC;gBAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;gBAE1D,0CAA0C;gBAC1C,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;gBACjD,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBACjE,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBACnE,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;gBAE5E,GAAG,CAAC,IAAI,CAAC;oBACP,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM;oBAC1B,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS;oBAC9D,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxD,cAAc,EAAE,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9D,aAAa,EAAE,UAAU;iBAC1B,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;gBAC5C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,+BAA+B;oBACtC,OAAO,EAAG,KAAe,CAAC,OAAO;iBAClC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gCAAgC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YACjE,IAAI,CAAC;gBACH,MAAM,MAAM,GAA2B;oBACrC,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM;oBAC1B,uBAAuB,EAAE,GAAG,CAAC,IAAI,CAAC,uBAAuB;iBAC1D,CAAC;gBAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;gBAEzD,GAAG,CAAC,IAAI,CAAC;oBACP,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM;oBAC1B,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,oBAAoB;oBACxD,MAAM,EAAE,WAAW;iBACpB,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;gBACjD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,6BAA6B;oBACpC,OAAO,EAAG,KAAe,CAAC,OAAO;iBAClC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,kCAAkC;QAClC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YAC5C,IAAI,CAAC;gBACH,sEAAsE;gBACtE,GAAG,CAAC,IAAI,CAAC;oBACP,OAAO,EAAE,kCAAkC;oBAC3C,UAAU,EAAE,uCAAuC;iBACpD,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,sBAAsB;oBAC7B,OAAO,EAAG,KAAe,CAAC,OAAO;iBAClC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,iBAAiB;QACjB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAY,EAAE,GAAoB,EAAE,GAAqB,EAAE,IAA0B,EAAE,EAAE;YACrG,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACzC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,cAAc;QACd,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACxB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,oBAAoB;gBAC3B,mBAAmB,EAAE;oBACnB,aAAa;oBACb,UAAU;oBACV,oBAAoB;oBACpB,2BAA2B;oBAC3B,qCAAqC;iBACtC;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAE/B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE;gBAC9B,OAAO,CAAC,GAAG,CAAC,yDAAyD,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBAClF,OAAO,CAAC,GAAG,CAAC,0CAA0C,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC;gBACvE,OAAO,CAAC,GAAG,CAAC,sCAAsC,IAAI,CAAC,IAAI,SAAS,CAAC,CAAC;gBACtE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;gBAC9B,OAAO,CAAC,GAAG,CAAC,iCAAiC,IAAI,CAAC,IAAI,kBAAkB,CAAC,CAAC;gBAC1E,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;gBACxD,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;YAClF,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;CACF;AAED,+BAA+B;AAC/B,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACpD,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,MAAM,CAAC,CAAC;IACvD,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;IACtC,OAAO,CAAC,KAAK,EAAE,CAAC;AAClB,CAAC;AAED,OAAO,EAAE,WAAW,EAAE,CAAC"}