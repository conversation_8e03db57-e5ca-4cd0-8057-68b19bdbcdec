import { WebContent } from './types.js';
export declare class BrowserManager {
    private browser;
    private turndownService;
    private config;
    constructor(config?: {
        headless?: boolean;
        timeout?: number;
        userAgent?: string;
    });
    initialize(): Promise<void>;
    extractContent(url: string): Promise<WebContent>;
    extractMultiple(urls: string[], maxConcurrent?: number): Promise<WebContent[]>;
    searchPageContent(url: string, searchTerms: string[]): Promise<string[]>;
    takeScreenshot(url: string, outputPath?: string): Promise<Buffer>;
    checkPageAccessibility(url: string): Promise<{
        accessible: boolean;
        statusCode?: number;
        error?: string;
    }>;
    private createPage;
    private chunkArray;
    close(): Promise<void>;
}
//# sourceMappingURL=browser.d.ts.map