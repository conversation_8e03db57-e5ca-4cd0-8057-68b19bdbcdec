import { SearchResult, SearchConfig } from './types.js';
export declare class SearchManager {
    private config;
    constructor(config: SearchConfig);
    search(query: string, maxResults?: number): Promise<SearchResult[]>;
    private searchGoogle;
    private searchBing;
    private searchDuckDuckGo;
    private searchSerper;
    searchMultiple(queries: string[], maxResultsPerQuery?: number): Promise<SearchResult[]>;
    searchWithFilters(query: string, options?: {
        excludeDomains?: string[];
        includeDomains?: string[];
        dateRange?: 'day' | 'week' | 'month' | 'year';
        fileType?: string;
        language?: string;
    }): Promise<SearchResult[]>;
    generateSearchQueries(originalQuery: string): string[];
}
//# sourceMappingURL=searcher.d.ts.map