{"version": 3, "file": "browser.js", "sourceRoot": "", "sources": ["../src/browser.ts"], "names": [], "mappings": "AAAA,OAAO,SAA4B,MAAM,WAAW,CAAC;AACrD,OAAO,eAAe,MAAM,UAAU,CAAC;AACvC,OAAO,EAAc,aAAa,EAAE,MAAM,YAAY,CAAC;AACvD,OAAO,EAAE,KAAK,EAAE,mBAAmB,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AAElF,MAAM,OAAO,cAAc;IACjB,OAAO,GAAmB,IAAI,CAAC;IAC/B,eAAe,CAAkB;IACjC,MAAM,CAIZ;IAEF,YAAY,SAAuE,EAAE;QACnF,IAAI,CAAC,MAAM,GAAG;YACZ,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,IAAI;YACjC,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,KAAK;YAChC,SAAS,EAAE,MAAM,CAAC,SAAS;SAC5B,CAAC;QAEF,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC;YACzC,YAAY,EAAE,KAAK;YACnB,cAAc,EAAE,QAAQ;SACzB,CAAC,CAAC;QAEH,oDAAoD;QACpD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,eAAe,EAAE;YAC5C,MAAM,EAAE,GAAG;YACX,WAAW,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;gBAC7B,MAAM,IAAI,GAAI,IAA0B,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBAC9D,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC;YAClD,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC;gBACpC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,IAAI,EAAE;oBACJ,cAAc;oBACd,0BAA0B;oBAC1B,yBAAyB;oBACzB,iCAAiC;oBACjC,gBAAgB;oBAChB,aAAa;oBACb,eAAe;iBAChB;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,aAAa,CAAC,iCAAkC,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,GAAW;QAC9B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACrB,MAAM,IAAI,aAAa,CAAC,gBAAgB,GAAG,EAAE,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAExB,OAAO,KAAK,CAAC,KAAK,IAAI,EAAE;YACtB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YAErC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;oBACnB,SAAS,EAAE,cAAc;oBACzB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;iBAC7B,CAAC,CAAC;gBAEH,2BAA2B;gBAC3B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAEhC,uBAAuB;gBACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;oBACvC,mCAAmC;oBACnC,MAAM,OAAO,GAAG,QAAQ,CAAC,gBAAgB,CAAC,yDAAyD,CAAC,CAAC;oBACrG,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;oBAEnC,gCAAgC;oBAChC,MAAM,aAAa,GAAG;wBACpB,MAAM;wBACN,SAAS;wBACT,eAAe;wBACf,eAAe;wBACf,UAAU;wBACV,eAAe;wBACf,gBAAgB;wBAChB,UAAU;wBACV,OAAO;qBACR,CAAC;oBAEF,IAAI,WAAW,GAAG,IAAI,CAAC;oBACvB,KAAK,MAAM,QAAQ,IAAI,aAAa,EAAE,CAAC;wBACrC,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;wBACjD,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;4BAC9E,WAAW,GAAG,OAAO,CAAC;4BACtB,MAAM;wBACR,CAAC;oBACH,CAAC;oBAED,4CAA4C;oBAC5C,MAAM,cAAc,GAAG,WAAW,IAAI,QAAQ,CAAC,IAAI,CAAC;oBAEpD,OAAO;wBACL,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,EAAE;wBAC3B,IAAI,EAAE,cAAc,CAAC,SAAS;wBAC9B,IAAI,EAAE,cAAc,CAAC,WAAW,IAAI,EAAE;wBACtC,MAAM,EAAE,QAAQ,CAAC,aAAa,CAAC,qBAAqB,CAAC,EAAE,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE;wBACpF,aAAa,EAAE,QAAQ,CAAC,aAAa,CAAC,yCAAyC,CAAC,EAAE,YAAY,CAAC,SAAS,CAAC;4BAC3F,QAAQ,CAAC,aAAa,CAAC,mBAAmB,CAAC,EAAE,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE;wBACxF,QAAQ,EAAE,QAAQ,CAAC,eAAe,CAAC,IAAI,IAAI,EAAE;qBAC9C,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,2BAA2B;gBAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC7D,MAAM,SAAS,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAE7C,OAAO;oBACL,GAAG;oBACH,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,OAAO,EAAE,SAAS;oBAClB,QAAQ;oBACR,QAAQ,EAAE;wBACR,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,SAAS;wBACnC,cAAc,EAAE,OAAO,CAAC,aAAa,IAAI,SAAS;wBAClD,UAAU,EAAE,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM;wBACzC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS;qBACxC;oBACD,YAAY,EAAE,mBAAmB,EAAE;iBACpC,CAAC;YAEJ,CAAC;oBAAS,CAAC;gBACT,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;YACrB,CAAC;QACH,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IACd,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,IAAc,EAAE,gBAAwB,CAAC;QAC7D,MAAM,OAAO,GAAiB,EAAE,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAEpD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBACvC,IAAI,CAAC;oBACH,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;gBACxC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,kCAAkC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC9D,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACjD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,MAAM,EAAwB,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,GAAW,EAAE,WAAqB;QACxD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAElD,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAErC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC/C,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;gBAChC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,GAAW,EAAE,UAAmB;QACnD,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAErC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC;YAEpD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC;gBACvC,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,UAAU;aACjB,CAAC,CAAC;YAEH,OAAO,UAAoB,CAAC;QAC9B,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,GAAW;QAKtC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAErC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACpC,SAAS,EAAE,cAAc;gBACzB,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,OAAO;gBACL,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE;aAC/B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,UAAU,EAAE,KAAK;gBACjB,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC;QACJ,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,aAAa,CAAC,yBAAyB,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAE1C,6BAA6B;QAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACjD,CAAC;QAED,eAAe;QACf,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAEtD,kDAAkD;QAClD,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE;YACzB,MAAM,YAAY,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;YACxC,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBACpE,GAAG,CAAC,KAAK,EAAE,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,QAAQ,EAAE,CAAC;YACjB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,UAAU,CAAI,KAAU,EAAE,SAAiB;QACjD,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;IACH,CAAC;CACF"}