import { ResearchPlan, AIConfig } from './types.js';
export declare class ResearchPlanner {
    private aiConfig;
    private openai?;
    private anthropic?;
    constructor(aiConfig: AIConfig);
    private initializeAI;
    createResearchPlan(query: string, focusAreas?: string[]): Promise<ResearchPlan[]>;
    private buildPlanningPrompt;
    private callAI;
    private callOpenAI;
    private callAnthropic;
    private parsePlanResponse;
    private createFallbackPlan;
    refinePlan(currentPlan: ResearchPlan[], findings: string[], additionalInstructions?: string): Promise<ResearchPlan[]>;
    private mergePlans;
}
//# sourceMappingURL=planner.d.ts.map