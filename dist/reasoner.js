import { AIError } from './types.js';
import { generateId, getCurrentTimestamp } from './utils.js';
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
export class ResearchReasoner {
    aiConfig;
    openai;
    anthropic;
    constructor(aiConfig) {
        this.aiConfig = aiConfig;
        this.initializeAI();
    }
    initializeAI() {
        switch (this.aiConfig.provider) {
            case 'openai':
                if (!this.aiConfig.api_key) {
                    throw new AIError('OpenAI API key not provided');
                }
                this.openai = new OpenAI({
                    apiKey: this.aiConfig.api_key,
                    baseURL: this.aiConfig.base_url,
                });
                break;
            case 'anthropic':
                if (!this.aiConfig.api_key) {
                    throw new AIError('Anthropic API key not provided');
                }
                this.anthropic = new Anthropic({
                    apiKey: this.aiConfig.api_key,
                });
                break;
            default:
                throw new AIError(`Unsupported AI provider: ${this.aiConfig.provider}`);
        }
    }
    async analyzeFindings(task, newFindings) {
        const prompt = this.buildAnalysisPrompt(task, newFindings);
        try {
            const response = await this.callAI(prompt);
            return this.parseAnalysisResponse(response, task.id);
        }
        catch (error) {
            throw new AIError(`Failed to analyze findings: ${error.message}`);
        }
    }
    buildAnalysisPrompt(task, newFindings) {
        const completedTasks = task.plan.filter(p => p.status === 'completed');
        const pendingTasks = task.plan.filter(p => p.status === 'pending');
        const allFindings = [...task.findings, ...newFindings];
        return `You are an expert research analyst. Analyze the current research progress and determine next steps.

RESEARCH QUERY: "${task.query}"

COMPLETED SUBTASKS:
${completedTasks.map(t => `✓ ${t.description}`).join('\n')}

PENDING SUBTASKS:
${pendingTasks.map(t => `○ ${t.description} (Priority: ${t.priority})`).join('\n')}

CURRENT FINDINGS (${allFindings.length} total):
${allFindings.slice(-10).map(f => `
Source: ${f.source} (${f.url})
Title: ${f.title}
Content: ${f.content.substring(0, 300)}...
Relevance: ${f.relevance_score}/10
`).join('\n')}

PREVIOUS REASONING STEPS:
${task.reasoning_steps.slice(-3).map(r => `${r.step}: ${r.reasoning} → ${r.decision}`).join('\n')}

Based on this information, provide your analysis in the following JSON format:

{
  "reasoning": "Your detailed analysis of the current findings and what they reveal about the research query",
  "nextActions": [
    "Specific action 1 to take next",
    "Specific action 2 to take next"
  ],
  "shouldContinue": true/false,
  "missingInformation": [
    "Key information gap 1",
    "Key information gap 2"
  ],
  "keyInsights": [
    "Important insight 1 from current findings",
    "Important insight 2 from current findings"
  ],
  "confidenceLevel": 1-10,
  "recommendedSearchQueries": [
    "Specific search query 1 to fill gaps",
    "Specific search query 2 to fill gaps"
  ]
}

Consider:
1. What key insights have been discovered?
2. What critical information is still missing?
3. Are there contradictions that need resolution?
4. Should research continue or is enough information gathered?
5. What specific searches would be most valuable next?

Respond with ONLY the JSON object, no additional text.`;
    }
    async callAI(prompt) {
        switch (this.aiConfig.provider) {
            case 'openai':
                return this.callOpenAI(prompt);
            case 'anthropic':
                return this.callAnthropic(prompt);
            default:
                throw new AIError(`Unsupported AI provider: ${this.aiConfig.provider}`);
        }
    }
    async callOpenAI(prompt) {
        if (!this.openai) {
            throw new AIError('OpenAI client not initialized');
        }
        const response = await this.openai.chat.completions.create({
            model: this.aiConfig.model,
            messages: [{ role: 'user', content: prompt }],
            max_tokens: this.aiConfig.max_tokens,
            temperature: this.aiConfig.temperature,
        });
        const content = response.choices[0]?.message?.content;
        if (!content) {
            throw new AIError('No response from OpenAI');
        }
        return content;
    }
    async callAnthropic(prompt) {
        if (!this.anthropic) {
            throw new AIError('Anthropic client not initialized');
        }
        const response = await this.anthropic.messages.create({
            model: this.aiConfig.model,
            max_tokens: this.aiConfig.max_tokens,
            temperature: this.aiConfig.temperature,
            messages: [{ role: 'user', content: prompt }],
        });
        const content = response.content[0];
        if (content && content.type === 'text') {
            return content.text;
        }
        throw new AIError('Unexpected response type from Anthropic');
    }
    parseAnalysisResponse(response, taskId) {
        try {
            // Extract JSON from response
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            const jsonStr = jsonMatch ? jsonMatch[0] : response;
            const analysis = JSON.parse(jsonStr);
            const reasoning = {
                id: generateId(),
                task_id: taskId,
                step: 'Analysis of current findings',
                reasoning: analysis.reasoning || 'Analysis completed',
                decision: analysis.shouldContinue ? 'Continue research' : 'Proceed to synthesis',
                timestamp: getCurrentTimestamp(),
            };
            return {
                reasoning,
                nextActions: analysis.nextActions || [],
                shouldContinue: analysis.shouldContinue ?? true,
                missingInformation: analysis.missingInformation || [],
            };
        }
        catch (error) {
            console.warn('Failed to parse analysis response, using fallback:', error);
            // Fallback reasoning
            const reasoning = {
                id: generateId(),
                task_id: taskId,
                step: 'Fallback analysis',
                reasoning: 'Unable to parse AI analysis, proceeding with basic assessment',
                decision: 'Continue research with next pending task',
                timestamp: getCurrentTimestamp(),
            };
            return {
                reasoning,
                nextActions: ['Continue with next pending research task'],
                shouldContinue: true,
                missingInformation: ['Unable to determine specific gaps'],
            };
        }
    }
    async evaluateCompleteness(task) {
        const prompt = `Evaluate the completeness of this research task:

QUERY: "${task.query}"

COMPLETED SUBTASKS: ${task.plan.filter(p => p.status === 'completed').length}/${task.plan.length}

FINDINGS SUMMARY:
- Total findings: ${task.findings.length}
- Average relevance: ${task.findings.length > 0 ? (task.findings.reduce((sum, f) => sum + f.relevance_score, 0) / task.findings.length).toFixed(1) : 0}/10
- Unique sources: ${new Set(task.findings.map(f => f.source)).size}

KEY FINDINGS:
${task.findings
            .sort((a, b) => b.relevance_score - a.relevance_score)
            .slice(0, 5)
            .map(f => `- ${f.title} (${f.relevance_score}/10)`)
            .join('\n')}

Evaluate completeness on a scale of 1-10 and provide recommendations.

Respond in JSON format:
{
  "isComplete": true/false,
  "completenessScore": 1-10,
  "reasoning": "Detailed explanation of completeness assessment",
  "recommendations": ["recommendation 1", "recommendation 2"]
}`;
        try {
            const response = await this.callAI(prompt);
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            const jsonStr = jsonMatch ? jsonMatch[0] : response;
            const evaluation = JSON.parse(jsonStr);
            return {
                isComplete: evaluation.isComplete ?? false,
                completenessScore: evaluation.completenessScore ?? 5,
                reasoning: evaluation.reasoning ?? 'Unable to assess completeness',
                recommendations: evaluation.recommendations ?? [],
            };
        }
        catch (error) {
            console.warn('Failed to evaluate completeness:', error);
            // Fallback evaluation based on simple metrics
            const completedTasks = task.plan.filter(p => p.status === 'completed').length;
            const completionRatio = completedTasks / task.plan.length;
            const hasFindings = task.findings.length > 0;
            const avgRelevance = task.findings.length > 0 ?
                task.findings.reduce((sum, f) => sum + f.relevance_score, 0) / task.findings.length : 0;
            const completenessScore = Math.round((completionRatio * 4) +
                (hasFindings ? 3 : 0) +
                (avgRelevance / 10 * 3));
            return {
                isComplete: completenessScore >= 7,
                completenessScore,
                reasoning: `Fallback assessment: ${completedTasks}/${task.plan.length} tasks completed, ${task.findings.length} findings with average relevance ${avgRelevance.toFixed(1)}/10`,
                recommendations: completenessScore < 7 ? ['Complete remaining research tasks', 'Gather more relevant sources'] : [],
            };
        }
    }
}
//# sourceMappingURL=reasoner.js.map