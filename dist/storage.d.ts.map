{"version": 3, "file": "storage.d.ts", "sourceRoot": "", "sources": ["../src/storage.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAEpF,qBAAa,cAAc;IACzB,OAAO,CAAC,EAAE,CAAmB;IAC7B,OAAO,CAAC,KAAK,CAA8D;IAC3E,OAAO,CAAC,KAAK,CAAgD;IAC7D,OAAO,CAAC,KAAK,CAAkD;gBAEnD,YAAY,EAAE,MAAM;IAO1B,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;YAInB,YAAY;IAgDpB,QAAQ,CAAC,IAAI,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;IAkB3C,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IA0BrD,WAAW,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAiB5C,WAAW,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;IAkB/C,iBAAiB,CAAC,IAAI,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IAerD,iBAAiB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAgB3D,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAO/E,cAAc,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;IAkBzC,eAAe,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAwBjE,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;CAW7B"}