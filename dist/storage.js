import sqlite3 from 'sqlite3';
import { promisify } from 'util';
export class StorageManager {
    db;
    dbRun;
    dbGet;
    dbAll;
    constructor(databasePath) {
        this.db = new sqlite3.Database(databasePath);
        this.dbRun = promisify(this.db.run.bind(this.db));
        this.dbGet = promisify(this.db.get.bind(this.db));
        this.dbAll = promisify(this.db.all.bind(this.db));
    }
    async initialize() {
        await this.createTables();
    }
    async createTables() {
        // Research tasks table
        await this.dbRun(`
      CREATE TABLE IF NOT EXISTS research_tasks (
        id TEXT PRIMARY KEY,
        query TEXT NOT NULL,
        status TEXT NOT NULL,
        plan TEXT NOT NULL,
        report TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);
        // Findings table
        await this.dbRun(`
      CREATE TABLE IF NOT EXISTS findings (
        id TEXT PRIMARY KEY,
        task_id TEXT NOT NULL,
        source TEXT NOT NULL,
        url TEXT NOT NULL,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        relevance_score REAL NOT NULL,
        timestamp TEXT NOT NULL,
        FOREIGN KEY (task_id) REFERENCES research_tasks (id)
      )
    `);
        // Reasoning steps table
        await this.dbRun(`
      CREATE TABLE IF NOT EXISTS reasoning_steps (
        id TEXT PRIMARY KEY,
        task_id TEXT NOT NULL,
        step TEXT NOT NULL,
        reasoning TEXT NOT NULL,
        decision TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        FOREIGN KEY (task_id) REFERENCES research_tasks (id)
      )
    `);
        // Create indexes for better performance
        await this.dbRun('CREATE INDEX IF NOT EXISTS idx_findings_task_id ON findings (task_id)');
        await this.dbRun('CREATE INDEX IF NOT EXISTS idx_reasoning_task_id ON reasoning_steps (task_id)');
        await this.dbRun('CREATE INDEX IF NOT EXISTS idx_tasks_status ON research_tasks (status)');
    }
    async saveTask(task) {
        const planJson = JSON.stringify(task.plan);
        await this.dbRun(`
      INSERT OR REPLACE INTO research_tasks 
      (id, query, status, plan, report, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
            task.id,
            task.query,
            task.status,
            planJson,
            task.report || null,
            task.created_at,
            task.updated_at
        ]);
    }
    async getTask(taskId) {
        const taskRow = await this.dbGet('SELECT * FROM research_tasks WHERE id = ?', [taskId]);
        if (!taskRow) {
            return null;
        }
        const findings = await this.getFindings(taskId);
        const reasoningSteps = await this.getReasoningSteps(taskId);
        return {
            id: taskRow.id,
            query: taskRow.query,
            status: taskRow.status,
            plan: JSON.parse(taskRow.plan),
            findings,
            reasoning_steps: reasoningSteps,
            report: taskRow.report || undefined,
            created_at: taskRow.created_at,
            updated_at: taskRow.updated_at,
        };
    }
    async saveFinding(finding) {
        await this.dbRun(`
      INSERT OR REPLACE INTO findings 
      (id, task_id, source, url, title, content, relevance_score, timestamp)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
            finding.id,
            finding.task_id,
            finding.source,
            finding.url,
            finding.title,
            finding.content,
            finding.relevance_score,
            finding.timestamp
        ]);
    }
    async getFindings(taskId) {
        const rows = await this.dbAll('SELECT * FROM findings WHERE task_id = ? ORDER BY timestamp DESC', [taskId]);
        return rows.map(row => ({
            id: row.id,
            task_id: row.task_id,
            source: row.source,
            url: row.url,
            title: row.title,
            content: row.content,
            relevance_score: row.relevance_score,
            timestamp: row.timestamp,
        }));
    }
    async saveReasoningStep(step) {
        await this.dbRun(`
      INSERT INTO reasoning_steps 
      (id, task_id, step, reasoning, decision, timestamp)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
            step.id,
            step.task_id,
            step.step,
            step.reasoning,
            step.decision,
            step.timestamp
        ]);
    }
    async getReasoningSteps(taskId) {
        const rows = await this.dbAll('SELECT * FROM reasoning_steps WHERE task_id = ? ORDER BY timestamp ASC', [taskId]);
        return rows.map(row => ({
            id: row.id,
            task_id: row.task_id,
            step: row.step,
            reasoning: row.reasoning,
            decision: row.decision,
            timestamp: row.timestamp,
        }));
    }
    async updateTaskStatus(taskId, status) {
        await this.dbRun('UPDATE research_tasks SET status = ?, updated_at = ? WHERE id = ?', [status, new Date().toISOString(), taskId]);
    }
    async getActiveTasks() {
        const rows = await this.dbAll(`
      SELECT * FROM research_tasks 
      WHERE status IN ('planning', 'searching', 'reasoning', 'synthesizing')
      ORDER BY created_at DESC
    `);
        const tasks = [];
        for (const row of rows) {
            const task = await this.getTask(row.id);
            if (task) {
                tasks.push(task);
            }
        }
        return tasks;
    }
    async getTaskProgress(taskId) {
        const task = await this.getTask(taskId);
        if (!task) {
            return null;
        }
        const completedSubtasks = task.plan.filter(p => p.status === 'completed').length;
        const totalSubtasks = task.plan.length;
        const progressPercentage = totalSubtasks > 0 ? (completedSubtasks / totalSubtasks) * 100 : 0;
        const currentStep = task.plan.find(p => p.status === 'in_progress')?.description ||
            task.status.charAt(0).toUpperCase() + task.status.slice(1);
        return {
            task_id: taskId,
            status: task.status,
            progress_percentage: Math.round(progressPercentage),
            current_step: currentStep,
            completed_subtasks: completedSubtasks,
            total_subtasks: totalSubtasks,
            findings_count: task.findings.length,
        };
    }
    async close() {
        return new Promise((resolve, reject) => {
            this.db.close((err) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve();
                }
            });
        });
    }
}
//# sourceMappingURL=storage.js.map