import { AIError } from './types.js';
import { getCurrentTimestamp, extractKeywords, formatDuration } from './utils.js';
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
export class ResearchSynthesizer {
    aiConfig;
    openai;
    anthropic;
    constructor(aiConfig) {
        this.aiConfig = aiConfig;
        this.initializeAI();
    }
    initializeAI() {
        switch (this.aiConfig.provider) {
            case 'openai':
                if (!this.aiConfig.api_key) {
                    throw new AIError('OpenAI API key not provided');
                }
                this.openai = new OpenAI({
                    apiKey: this.aiConfig.api_key,
                    baseURL: this.aiConfig.base_url,
                });
                break;
            case 'anthropic':
                if (!this.aiConfig.api_key) {
                    throw new AIError('Anthropic API key not provided');
                }
                this.anthropic = new Anthropic({
                    apiKey: this.aiConfig.api_key,
                });
                break;
            default:
                throw new AIError(`Unsupported AI provider: ${this.aiConfig.provider}`);
        }
    }
    async generateReport(task) {
        const prompt = this.buildSynthesisPrompt(task);
        try {
            const response = await this.callAI(prompt);
            return this.formatFinalReport(response, task);
        }
        catch (error) {
            throw new AIError(`Failed to generate report: ${error.message}`);
        }
    }
    buildSynthesisPrompt(task) {
        const topFindings = task.findings
            .sort((a, b) => b.relevance_score - a.relevance_score)
            .slice(0, 20);
        const keyInsights = task.reasoning_steps
            .map(r => r.reasoning)
            .join('\n');
        return `You are an expert research analyst. Create a comprehensive research report based on the following information.

RESEARCH QUERY: "${task.query}"

RESEARCH METHODOLOGY:
${task.plan.map(p => `${p.status === 'completed' ? '✓' : '○'} ${p.description}`).join('\n')}

KEY FINDINGS (Top ${topFindings.length} most relevant):
${topFindings.map(f => `
Source: ${f.source}
URL: ${f.url}
Title: ${f.title}
Relevance: ${f.relevance_score}/10
Content: ${f.content.substring(0, 500)}...
`).join('\n')}

RESEARCH INSIGHTS:
${keyInsights}

Create a comprehensive research report with the following structure:

# Research Report: [Title based on query]

## Executive Summary
[2-3 paragraph summary of key findings and conclusions]

## Introduction
[Brief overview of the research question and methodology]

## Key Findings
[Organize findings into 3-5 main themes with supporting evidence]

### [Theme 1]
[Detailed analysis with citations]

### [Theme 2]
[Detailed analysis with citations]

### [Theme 3]
[Detailed analysis with citations]

## Analysis and Insights
[Deeper analysis connecting the findings, identifying patterns, contradictions, and implications]

## Conclusions
[Clear conclusions answering the original research question]

## Recommendations
[Actionable recommendations based on the research]

## Sources and References
[List of all sources with URLs]

## Limitations and Further Research
[Acknowledge any limitations and suggest areas for future investigation]

Guidelines:
1. Use clear, professional language
2. Support all claims with evidence from the findings
3. Include specific data, statistics, and quotes where relevant
4. Cite sources using [Source Name](URL) format
5. Maintain objectivity and acknowledge different perspectives
6. Ensure the report directly addresses the original research query
7. Make the report comprehensive but readable (aim for 2000-4000 words)

Write the complete report in Markdown format.`;
    }
    async callAI(prompt) {
        // Use higher token limit for report generation
        const reportConfig = {
            ...this.aiConfig,
            max_tokens: Math.min(this.aiConfig.max_tokens * 2, 8000),
            temperature: 0.3, // Lower temperature for more consistent reports
        };
        switch (this.aiConfig.provider) {
            case 'openai':
                return this.callOpenAI(prompt, reportConfig);
            case 'anthropic':
                return this.callAnthropic(prompt, reportConfig);
            default:
                throw new AIError(`Unsupported AI provider: ${this.aiConfig.provider}`);
        }
    }
    async callOpenAI(prompt, config) {
        if (!this.openai) {
            throw new AIError('OpenAI client not initialized');
        }
        const response = await this.openai.chat.completions.create({
            model: config.model,
            messages: [{ role: 'user', content: prompt }],
            max_tokens: config.max_tokens,
            temperature: config.temperature,
        });
        const content = response.choices[0]?.message?.content;
        if (!content) {
            throw new AIError('No response from OpenAI');
        }
        return content;
    }
    async callAnthropic(prompt, config) {
        if (!this.anthropic) {
            throw new AIError('Anthropic client not initialized');
        }
        const response = await this.anthropic.messages.create({
            model: config.model,
            max_tokens: config.max_tokens,
            temperature: config.temperature,
            messages: [{ role: 'user', content: prompt }],
        });
        const content = response.content[0];
        if (content && content.type === 'text') {
            return content.text;
        }
        throw new AIError('Unexpected response type from Anthropic');
    }
    formatFinalReport(report, task) {
        const startTime = new Date(task.created_at);
        const endTime = new Date();
        const duration = formatDuration(startTime, endTime);
        const metadata = `---
Research Query: ${task.query}
Generated: ${getCurrentTimestamp()}
Duration: ${duration}
Sources: ${task.findings.length}
Unique Domains: ${new Set(task.findings.map(f => new URL(f.url).hostname)).size}
---

`;
        return metadata + report;
    }
    async generateExecutiveSummary(task) {
        const topFindings = task.findings
            .sort((a, b) => b.relevance_score - a.relevance_score)
            .slice(0, 10);
        const prompt = `Create a concise executive summary for this research:

QUERY: "${task.query}"

TOP FINDINGS:
${topFindings.map(f => `- ${f.title}: ${f.content.substring(0, 200)}...`).join('\n')}

Create a 2-3 paragraph executive summary that:
1. Clearly states what was researched
2. Highlights the most important findings
3. Provides key conclusions
4. Is accessible to non-experts

Keep it under 300 words and focus on actionable insights.`;
        try {
            const response = await this.callAI(prompt);
            return response.trim();
        }
        catch (error) {
            throw new AIError(`Failed to generate executive summary: ${error.message}`);
        }
    }
    async generateKeyInsights(findings) {
        if (findings.length === 0) {
            return [];
        }
        const topFindings = findings
            .sort((a, b) => b.relevance_score - a.relevance_score)
            .slice(0, 15);
        const prompt = `Extract 5-7 key insights from these research findings:

${topFindings.map(f => `
Title: ${f.title}
Source: ${f.source}
Content: ${f.content.substring(0, 400)}...
`).join('\n')}

Provide insights as a JSON array of strings. Each insight should be:
1. Specific and actionable
2. Supported by the evidence
3. Clearly stated in 1-2 sentences
4. Focused on the most important discoveries

Format: ["insight 1", "insight 2", "insight 3", ...]`;
        try {
            const response = await this.callAI(prompt);
            const jsonMatch = response.match(/\[[\s\S]*\]/);
            const jsonStr = jsonMatch ? jsonMatch[0] : response;
            const insights = JSON.parse(jsonStr);
            return Array.isArray(insights) ? insights : [];
        }
        catch (error) {
            console.warn('Failed to generate structured insights:', error);
            // Fallback: extract key themes from findings
            const allText = findings.map(f => f.content).join(' ');
            const keywords = extractKeywords(allText, 10);
            return keywords.map(keyword => `Research indicates significant relevance of ${keyword} to the query`).slice(0, 5);
        }
    }
    async generateRecommendations(task) {
        const prompt = `Based on this research, provide 3-5 specific, actionable recommendations:

QUERY: "${task.query}"

KEY FINDINGS SUMMARY:
${task.findings.slice(0, 10).map(f => `- ${f.title}: ${f.content.substring(0, 150)}...`).join('\n')}

RESEARCH INSIGHTS:
${task.reasoning_steps.slice(-3).map(r => r.reasoning).join('\n')}

Provide recommendations as a JSON array. Each recommendation should be:
1. Specific and actionable
2. Based on the research evidence
3. Practical to implement
4. Clearly stated

Format: ["recommendation 1", "recommendation 2", ...]`;
        try {
            const response = await this.callAI(prompt);
            const jsonMatch = response.match(/\[[\s\S]*\]/);
            const jsonStr = jsonMatch ? jsonMatch[0] : response;
            const recommendations = JSON.parse(jsonStr);
            return Array.isArray(recommendations) ? recommendations : [];
        }
        catch (error) {
            console.warn('Failed to generate recommendations:', error);
            return [
                'Review the research findings for actionable insights',
                'Consider conducting additional research on identified gaps',
                'Implement findings based on organizational priorities',
            ];
        }
    }
}
//# sourceMappingURL=synthesizer.js.map