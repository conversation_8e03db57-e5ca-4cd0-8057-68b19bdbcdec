import { ResearchTask, Finding, ReasoningStep, AIConfig } from './types.js';
export declare class ResearchReasoner {
    private aiConfig;
    private openai?;
    private anthropic?;
    constructor(aiConfig: AIConfig);
    private initializeAI;
    analyzeFindings(task: ResearchTask, newFindings: Finding[]): Promise<{
        reasoning: ReasoningStep;
        nextActions: string[];
        shouldContinue: boolean;
        missingInformation: string[];
    }>;
    private buildAnalysisPrompt;
    private callAI;
    private callOpenAI;
    private callAnthropic;
    private parseAnalysisResponse;
    evaluateCompleteness(task: ResearchTask): Promise<{
        isComplete: boolean;
        completenessScore: number;
        reasoning: string;
        recommendations: string[];
    }>;
}
//# sourceMappingURL=reasoner.d.ts.map