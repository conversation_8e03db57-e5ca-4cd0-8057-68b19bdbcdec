{"version": 3, "file": "storage.js", "sourceRoot": "", "sources": ["../src/storage.ts"], "names": [], "mappings": "AAAA,OAAO,OAAO,MAAM,SAAS,CAAC;AAC9B,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AAGjC,MAAM,OAAO,cAAc;IACjB,EAAE,CAAmB;IACrB,KAAK,CAA8D;IACnE,KAAK,CAAgD;IACrD,KAAK,CAAkD;IAE/D,YAAY,YAAoB;QAC9B,IAAI,CAAC,EAAE,GAAG,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC7C,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,uBAAuB;QACvB,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;KAUhB,CAAC,CAAC;QAEH,iBAAiB;QACjB,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;;KAYhB,CAAC,CAAC;QAEH,wBAAwB;QACxB,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;KAUhB,CAAC,CAAC;QAEH,wCAAwC;QACxC,MAAM,IAAI,CAAC,KAAK,CAAC,uEAAuE,CAAC,CAAC;QAC1F,MAAM,IAAI,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAC;QAClG,MAAM,IAAI,CAAC,KAAK,CAAC,wEAAwE,CAAC,CAAC;IAC7F,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,IAAkB;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,MAAM,IAAI,CAAC,KAAK,CAAC;;;;KAIhB,EAAE;YACD,IAAI,CAAC,EAAE;YACP,IAAI,CAAC,KAAK;YACV,IAAI,CAAC,MAAM;YACX,QAAQ;YACR,IAAI,CAAC,MAAM,IAAI,IAAI;YACnB,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,UAAU;SAChB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAc;QAC1B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAC9B,2CAA2C,EAC3C,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAChD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAE5D,OAAO;YACL,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;YAC9B,QAAQ;YACR,eAAe,EAAE,cAAc;YAC/B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,SAAS;YACnC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,UAAU,EAAE,OAAO,CAAC,UAAU;SAC/B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAgB;QAChC,MAAM,IAAI,CAAC,KAAK,CAAC;;;;KAIhB,EAAE;YACD,OAAO,CAAC,EAAE;YACV,OAAO,CAAC,OAAO;YACf,OAAO,CAAC,MAAM;YACd,OAAO,CAAC,GAAG;YACX,OAAO,CAAC,KAAK;YACb,OAAO,CAAC,OAAO;YACf,OAAO,CAAC,eAAe;YACvB,OAAO,CAAC,SAAS;SAClB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAc;QAC9B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,kEAAkE,EAClE,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACtB,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,KAAK,EAAE,GAAG,CAAC,KAAK;YAChB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,eAAe,EAAE,GAAG,CAAC,eAAe;YACpC,SAAS,EAAE,GAAG,CAAC,SAAS;SACzB,CAAC,CAAC,CAAC;IACN,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,IAAmB;QACzC,MAAM,IAAI,CAAC,KAAK,CAAC;;;;KAIhB,EAAE;YACD,IAAI,CAAC,EAAE;YACP,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,SAAS;YACd,IAAI,CAAC,QAAQ;YACb,IAAI,CAAC,SAAS;SACf,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc;QACpC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,wEAAwE,EACxE,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACtB,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,SAAS,EAAE,GAAG,CAAC,SAAS;SACzB,CAAC,CAAC,CAAC;IACN,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,MAA8B;QACnE,MAAM,IAAI,CAAC,KAAK,CACd,mEAAmE,EACnE,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,MAAM,CAAC,CAC3C,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;;;;KAI7B,CAAC,CAAC;QAEH,MAAM,KAAK,GAAmB,EAAE,CAAC;QACjC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACxC,IAAI,IAAI,EAAE,CAAC;gBACT,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnB,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAc;QAClC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM,CAAC;QACjF,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QACvC,MAAM,kBAAkB,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7F,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,aAAa,CAAC,EAAE,WAAW;YAC9D,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAE7E,OAAO;YACL,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;YACnD,YAAY,EAAE,WAAW;YACzB,kBAAkB,EAAE,iBAAiB;YACrC,cAAc,EAAE,aAAa;YAC7B,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;SACrC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,KAAK;QACT,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gBACpB,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF"}