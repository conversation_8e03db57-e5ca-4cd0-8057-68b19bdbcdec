{"version": 3, "file": "searcher.js", "sourceRoot": "", "sources": ["../src/searcher.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAA8B,WAAW,EAAE,MAAM,YAAY,CAAC;AACrE,OAAO,EAAE,KAAK,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;AAE7E,MAAM,OAAO,aAAa;IAChB,MAAM,CAAe;IAE7B,YAAY,MAAoB;QAC9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,KAAa,EAAE,UAAmB;QAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QAEpD,IAAI,CAAC;YACH,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC7B,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC/C,KAAK,MAAM;oBACT,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC7C,KAAK,YAAY;oBACf,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBACnD,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC/C;oBACE,MAAM,IAAI,WAAW,CAAC,gCAAgC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YAClF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,WAAW,EAAE,CAAC;gBACjC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,WAAW,CAAC,kBAAmB,KAAe,CAAC,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QACjH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAa,EAAE,UAAkB;QAC1D,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,IAAI,WAAW,CAAC,oCAAoC,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,KAAK,CAAC,KAAK,IAAI,EAAE;YACtB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,4CAA4C,EAAE;gBAC7E,MAAM,EAAE;oBACN,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;oBACxB,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB;oBACvC,CAAC,EAAE,KAAK;oBACR,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC;oBAC7B,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;iBACjD;gBACD,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACzB,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC7C,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,GAAG,EAAE,IAAI,CAAC,IAAI;gBACd,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;gBAC3B,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,mBAAmB,EAAE;aACjC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,KAAa,EAAE,UAAkB;QACxD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,IAAI,WAAW,CAAC,kCAAkC,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,KAAK,CAAC,KAAK,IAAI,EAAE;YACtB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,4CAA4C,EAAE;gBAC7E,OAAO,EAAE;oBACP,2BAA2B,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;iBACjD;gBACD,MAAM,EAAE;oBACN,CAAC,EAAE,KAAK;oBACR,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC;oBAC/B,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;iBACvD;gBACD,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;gBACnC,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBACtD,KAAK,EAAE,IAAI,CAAC,IAAI;gBAChB,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;gBAC3B,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,mBAAmB,EAAE;aACjC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,KAAa,EAAE,UAAkB;QAC9D,OAAO,KAAK,CAAC,KAAK,IAAI,EAAE;YACtB,yDAAyD;YACzD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,6BAA6B,EAAE;gBAC9D,MAAM,EAAE;oBACN,CAAC,EAAE,KAAK;oBACR,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE,GAAG;oBACZ,aAAa,EAAE,GAAG;iBACnB;gBACD,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,MAAM,OAAO,GAAmB,EAAE,CAAC;YAEnC,4BAA4B;YAC5B,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC3B,OAAO,CAAC,IAAI,CAAC;oBACX,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK;oBACrC,GAAG,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE;oBACpC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;oBAC/B,MAAM,EAAE,YAAY;oBACpB,SAAS,EAAE,mBAAmB,EAAE;iBACjC,CAAC,CAAC;YACL,CAAC;YAED,qBAAqB;YACrB,IAAI,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBAChC,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;oBACtF,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACjC,OAAO,CAAC,IAAI,CAAC;4BACX,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI;4BAC/C,GAAG,EAAE,KAAK,CAAC,QAAQ;4BACnB,OAAO,EAAE,KAAK,CAAC,IAAI;4BACnB,MAAM,EAAE,YAAY;4BACpB,SAAS,EAAE,mBAAmB,EAAE;yBACjC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAa,EAAE,UAAkB;QAC1D,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,IAAI,WAAW,CAAC,6BAA6B,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,KAAK,CAAC,KAAK,IAAI,EAAE;YACtB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,kCAAkC,EAAE;gBACpE,CAAC,EAAE,KAAK;gBACR,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;aAC/B,EAAE;gBACD,OAAO,EAAE;oBACP,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;oBAChC,cAAc,EAAE,kBAAkB;iBACnC;gBACD,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC3B,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC/C,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,GAAG,EAAE,IAAI,CAAC,IAAI;gBACd,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;gBAC3B,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,mBAAmB,EAAE;aACjC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAAiB,EAAE,qBAA6B,CAAC;QACpE,MAAM,UAAU,GAAmB,EAAE,CAAC;QAEtC,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;gBAC7D,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;YAC9B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,4BAA4B,KAAK,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC3D,8BAA8B;YAChC,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,MAAM,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACnE,OAAO,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,KAAa,EACb,UAMI,EAAE;QAEN,IAAI,aAAa,GAAG,KAAK,CAAC;QAE1B,qBAAqB;QACrB,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC3B,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC5C,aAAa,IAAI,UAAU,MAAM,EAAE,CAAC;YACtC,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvF,aAAa,IAAI,KAAK,UAAU,GAAG,CAAC;QACtC,CAAC;QAED,uBAAuB;QACvB,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,aAAa,IAAI,aAAa,OAAO,CAAC,QAAQ,EAAE,CAAC;QACnD,CAAC;QAED,wCAAwC;QACxC,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC1D,aAAa,IAAI,SAAS,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC/C,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACpC,CAAC;IAED,qBAAqB,CAAC,aAAqB;QACzC,MAAM,OAAO,GAAG,CAAC,aAAa,CAAC,CAAC;QAEhC,iBAAiB;QACjB,OAAO,CAAC,IAAI,CAAC,IAAI,aAAa,GAAG,CAAC,CAAC,CAAC,eAAe;QACnD,OAAO,CAAC,IAAI,CAAC,GAAG,aAAa,WAAW,CAAC,CAAC;QAC1C,OAAO,CAAC,IAAI,CAAC,GAAG,aAAa,WAAW,CAAC,CAAC;QAC1C,OAAO,CAAC,IAAI,CAAC,GAAG,aAAa,WAAW,CAAC,CAAC;QAC1C,OAAO,CAAC,IAAI,CAAC,GAAG,aAAa,QAAQ,CAAC,CAAC;QACvC,OAAO,CAAC,IAAI,CAAC,WAAW,aAAa,EAAE,CAAC,CAAC;QACzC,OAAO,CAAC,IAAI,CAAC,GAAG,aAAa,aAAa,CAAC,CAAC;QAC5C,OAAO,CAAC,IAAI,CAAC,GAAG,aAAa,WAAW,CAAC,CAAC;QAE1C,6CAA6C;QAC7C,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,kCAAkC;YAClC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC3C,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,qCAAqC;IACnE,CAAC;CACF"}