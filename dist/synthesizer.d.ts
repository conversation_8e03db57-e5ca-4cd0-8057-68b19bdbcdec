import { ResearchTask, Finding, AIConfig } from './types.js';
export declare class ResearchSynthesizer {
    private aiConfig;
    private openai?;
    private anthropic?;
    constructor(aiConfig: AIConfig);
    private initializeAI;
    generateReport(task: ResearchTask): Promise<string>;
    private buildSynthesisPrompt;
    private callAI;
    private callOpenAI;
    private callAnthropic;
    private formatFinalReport;
    generateExecutiveSummary(task: ResearchTask): Promise<string>;
    generateKeyInsights(findings: Finding[]): Promise<string[]>;
    generateRecommendations(task: ResearchTask): Promise<string[]>;
}
//# sourceMappingURL=synthesizer.d.ts.map