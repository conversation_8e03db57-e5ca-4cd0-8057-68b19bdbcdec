#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema, } from '@modelcontextprotocol/sdk/types.js';
// import { z } from 'zod';
import dotenv from 'dotenv';
import { StorageManager } from './storage.js';
import { SearchManager } from './searcher.js';
import { BrowserManager } from './browser.js';
import { ResearchPlanner } from './planner.js';
import { ResearchReasoner } from './reasoner.js';
import { ResearchSynthesizer } from './synthesizer.js';
import { ConfigSchema, ResearchError, } from './types.js';
import { generateId, getCurrentTimestamp, calculateRelevanceScore, validateConfig, } from './utils.js';
// Load environment variables
dotenv.config();
class DeepResearchServer {
    server;
    storage;
    searcher;
    browser;
    planner;
    reasoner;
    synthesizer;
    config;
    activeTasks = new Map();
    constructor() {
        this.config = this.loadConfig();
        validateConfig(this.config);
        this.server = new Server({
            name: 'deep-research-mcp-server',
            version: '1.0.0',
        }, {
            capabilities: {
                tools: {},
            },
        });
        // Initialize components
        this.storage = new StorageManager(this.config.storage.database_path);
        this.searcher = new SearchManager(this.config.search);
        this.browser = new BrowserManager(this.config.browser);
        this.planner = new ResearchPlanner(this.config.ai);
        this.reasoner = new ResearchReasoner(this.config.ai);
        this.synthesizer = new ResearchSynthesizer(this.config.ai);
        this.setupHandlers();
    }
    loadConfig() {
        const config = {
            ai: {
                provider: process.env.AI_PROVIDER || 'openai',
                model: process.env.AI_MODEL || 'gpt-4',
                api_key: process.env.AI_API_KEY,
                base_url: process.env.AI_BASE_URL,
                max_tokens: parseInt(process.env.AI_MAX_TOKENS || '4000'),
                temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),
            },
            search: {
                provider: process.env.SEARCH_PROVIDER || 'serper',
                api_key: process.env.SEARCH_API_KEY,
                max_results: parseInt(process.env.SEARCH_MAX_RESULTS || '10'),
                safe_search: process.env.SEARCH_SAFE_SEARCH !== 'false',
            },
            browser: {
                headless: process.env.BROWSER_HEADLESS !== 'false',
                timeout: parseInt(process.env.BROWSER_TIMEOUT || '30000'),
                user_agent: process.env.BROWSER_USER_AGENT,
            },
            storage: {
                database_path: process.env.DATABASE_PATH || './research.db',
            },
            limits: {
                max_concurrent_searches: parseInt(process.env.MAX_CONCURRENT_SEARCHES || '3'),
                max_pages_per_task: parseInt(process.env.MAX_PAGES_PER_TASK || '50'),
                max_task_duration_minutes: parseInt(process.env.MAX_TASK_DURATION_MINUTES || '30'),
            },
        };
        return ConfigSchema.parse(config);
    }
    setupHandlers() {
        this.server.setRequestHandler(ListToolsRequestSchema, async () => {
            return {
                tools: [
                    {
                        name: 'deep_research',
                        description: 'Perform comprehensive deep research on any topic, similar to Google Gemini Deep Research. Automatically plans research, searches the web, analyzes findings, and generates detailed reports.',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                query: {
                                    type: 'string',
                                    description: 'The research question or topic to investigate',
                                },
                                max_pages: {
                                    type: 'number',
                                    description: 'Maximum number of web pages to analyze (default: 20)',
                                    default: 20,
                                },
                                focus_areas: {
                                    type: 'array',
                                    items: { type: 'string' },
                                    description: 'Specific areas to focus the research on',
                                },
                                exclude_domains: {
                                    type: 'array',
                                    items: { type: 'string' },
                                    description: 'Domains to exclude from search results',
                                },
                                language: {
                                    type: 'string',
                                    description: 'Preferred language for search results (default: en)',
                                    default: 'en',
                                },
                            },
                            required: ['query'],
                        },
                    },
                    {
                        name: 'get_research_status',
                        description: 'Get the current status and progress of a research task',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                task_id: {
                                    type: 'string',
                                    description: 'The ID of the research task',
                                },
                            },
                            required: ['task_id'],
                        },
                    },
                    {
                        name: 'continue_research',
                        description: 'Continue or refine an existing research task with additional instructions',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                task_id: {
                                    type: 'string',
                                    description: 'The ID of the research task to continue',
                                },
                                additional_instructions: {
                                    type: 'string',
                                    description: 'Additional instructions or focus areas for the research',
                                },
                            },
                            required: ['task_id'],
                        },
                    },
                ],
            };
        });
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            try {
                switch (name) {
                    case 'deep_research':
                        return await this.handleDeepResearch(args);
                    case 'get_research_status':
                        return await this.handleGetResearchStatus(args);
                    case 'continue_research':
                        return await this.handleContinueResearch(args);
                    default:
                        throw new ResearchError(`Unknown tool: ${name}`, 'UNKNOWN_TOOL');
                }
            }
            catch (error) {
                if (error instanceof ResearchError) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `Error: ${error.message}`,
                            },
                        ],
                        isError: true,
                    };
                }
                throw error;
            }
        });
    }
    async handleDeepResearch(params) {
        const taskId = generateId();
        const task = {
            id: taskId,
            query: params.query,
            status: 'planning',
            plan: [],
            findings: [],
            reasoning_steps: [],
            created_at: getCurrentTimestamp(),
            updated_at: getCurrentTimestamp(),
        };
        // Start the research process asynchronously
        const researchPromise = this.performResearch(task, params);
        this.activeTasks.set(taskId, researchPromise);
        // Clean up when done
        researchPromise.finally(() => {
            this.activeTasks.delete(taskId);
        });
        return {
            content: [
                {
                    type: 'text',
                    text: `🔍 **Deep Research Started**

**Task ID:** ${taskId}
**Query:** ${params.query}
**Status:** Planning research approach...

The research is running in the background. Use \`get_research_status\` with task ID "${taskId}" to check progress, or \`continue_research\` to refine the research.

This comprehensive research will:
1. 📋 Create a detailed research plan
2. 🔍 Search multiple sources across the web
3. 📖 Extract and analyze content from relevant pages
4. 🧠 Reason through findings iteratively
5. 📊 Generate a comprehensive research report

Estimated completion time: 5-15 minutes depending on query complexity.`,
                },
            ],
        };
    }
    async handleGetResearchStatus(params) {
        const progress = await this.storage.getTaskProgress(params.task_id);
        if (!progress) {
            return {
                content: [
                    {
                        type: 'text',
                        text: `❌ Research task "${params.task_id}" not found.`,
                    },
                ],
            };
        }
        const isActive = this.activeTasks.has(params.task_id);
        const statusEmoji = {
            planning: '📋',
            searching: '🔍',
            reasoning: '🧠',
            synthesizing: '📊',
            completed: '✅',
            failed: '❌',
        }[progress.status] || '⏳';
        let statusText = `${statusEmoji} **Research Status**

**Task ID:** ${params.task_id}
**Status:** ${progress.status.charAt(0).toUpperCase() + progress.status.slice(1)}
**Progress:** ${progress.progress_percentage}%
**Current Step:** ${progress.current_step}
**Completed Subtasks:** ${progress.completed_subtasks}/${progress.total_subtasks}
**Findings Collected:** ${progress.findings_count}
**Active:** ${isActive ? 'Yes' : 'No'}`;
        if (progress.estimated_completion_time) {
            statusText += `\n**Estimated Completion:** ${progress.estimated_completion_time}`;
        }
        // If completed, include the report
        if (progress.status === 'completed') {
            const task = await this.storage.getTask(params.task_id);
            if (task?.report) {
                statusText += `\n\n---\n\n${task.report}`;
            }
        }
        return {
            content: [
                {
                    type: 'text',
                    text: statusText,
                },
            ],
        };
    }
    async handleContinueResearch(params) {
        const task = await this.storage.getTask(params.task_id);
        if (!task) {
            return {
                content: [
                    {
                        type: 'text',
                        text: `❌ Research task "${params.task_id}" not found.`,
                    },
                ],
            };
        }
        if (this.activeTasks.has(params.task_id)) {
            return {
                content: [
                    {
                        type: 'text',
                        text: `⚠️ Research task "${params.task_id}" is already running. Please wait for it to complete or check its status.`,
                    },
                ],
            };
        }
        // Continue the research with additional instructions
        const continuePromise = this.continueResearch(task, params.additional_instructions);
        this.activeTasks.set(params.task_id, continuePromise);
        continuePromise.finally(() => {
            this.activeTasks.delete(params.task_id);
        });
        return {
            content: [
                {
                    type: 'text',
                    text: `🔄 **Research Continued**

**Task ID:** ${params.task_id}
**Additional Instructions:** ${params.additional_instructions || 'None'}

The research is continuing with your additional guidance. Use \`get_research_status\` to monitor progress.`,
                },
            ],
        };
    }
    async initialize() {
        await this.storage.initialize();
        console.error('Deep Research MCP Server initialized');
    }
    async run() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('Deep Research MCP Server running on stdio');
    }
    async performResearch(task, params) {
        try {
            // Phase 1: Planning
            await this.storage.updateTaskStatus(task.id, 'planning');
            const plan = await this.planner.createResearchPlan(task.query, params.focus_areas);
            task.plan = plan;
            task.updated_at = getCurrentTimestamp();
            await this.storage.saveTask(task);
            // Phase 2: Research execution
            await this.storage.updateTaskStatus(task.id, 'searching');
            await this.executeResearchPlan(task, params);
            // Phase 3: Final synthesis
            await this.storage.updateTaskStatus(task.id, 'synthesizing');
            const report = await this.synthesizer.generateReport(task);
            task.report = report;
            task.updated_at = getCurrentTimestamp();
            await this.storage.saveTask(task);
            await this.storage.updateTaskStatus(task.id, 'completed');
        }
        catch (error) {
            console.error(`Research failed for task ${task.id}:`, error);
            await this.storage.updateTaskStatus(task.id, 'failed');
            throw error;
        }
    }
    async executeResearchPlan(task, params) {
        const maxPages = params.max_pages || 20;
        let pagesProcessed = 0;
        for (const planItem of task.plan) {
            if (pagesProcessed >= maxPages)
                break;
            try {
                // Mark task as in progress
                planItem.status = 'in_progress';
                await this.storage.saveTask(task);
                // Generate search queries for this subtask
                const searchQueries = this.searcher.generateSearchQueries(planItem.description);
                // Search for relevant content
                const searchResults = await this.searcher.searchMultiple(searchQueries, 5);
                // Filter out excluded domains
                const filteredResults = params.exclude_domains
                    ? searchResults.filter(r => !params.exclude_domains.some(domain => r.url.includes(domain)))
                    : searchResults;
                // Extract content from top results
                const urlsToProcess = filteredResults
                    .slice(0, Math.min(5, maxPages - pagesProcessed))
                    .map(r => r.url);
                const webContents = await this.browser.extractMultiple(urlsToProcess, 3);
                pagesProcessed += webContents.length;
                // Convert to findings
                const findings = webContents.map(content => ({
                    id: generateId(),
                    task_id: task.id,
                    source: content.url,
                    url: content.url,
                    title: content.title,
                    content: content.content,
                    relevance_score: calculateRelevanceScore(task.query, content.title, content.content),
                    timestamp: getCurrentTimestamp(),
                }));
                // Save findings
                for (const finding of findings) {
                    await this.storage.saveFinding(finding);
                }
                task.findings.push(...findings);
                // Analyze findings and decide next steps
                await this.storage.updateTaskStatus(task.id, 'reasoning');
                const analysis = await this.reasoner.analyzeFindings(task, findings);
                // Save reasoning step
                await this.storage.saveReasoningStep(analysis.reasoning);
                task.reasoning_steps.push(analysis.reasoning);
                // Mark subtask as completed
                planItem.status = 'completed';
                await this.storage.saveTask(task);
                // Check if we should continue
                if (!analysis.shouldContinue) {
                    break;
                }
                await this.storage.updateTaskStatus(task.id, 'searching');
            }
            catch (error) {
                console.error(`Failed to execute plan item ${planItem.id}:`, error);
                planItem.status = 'failed';
                await this.storage.saveTask(task);
            }
        }
    }
    async continueResearch(task, additionalInstructions) {
        try {
            // Refine the plan based on current findings
            if (additionalInstructions) {
                const findingSummaries = task.findings.slice(-10).map(f => f.title);
                const refinedPlan = await this.planner.refinePlan(task.plan, findingSummaries, additionalInstructions);
                task.plan = refinedPlan;
                task.updated_at = getCurrentTimestamp();
                await this.storage.saveTask(task);
            }
            // Continue with remaining tasks
            await this.storage.updateTaskStatus(task.id, 'searching');
            await this.executeResearchPlan(task, { query: task.query, max_pages: 10 });
            // Generate updated report
            await this.storage.updateTaskStatus(task.id, 'synthesizing');
            const report = await this.synthesizer.generateReport(task);
            task.report = report;
            task.updated_at = getCurrentTimestamp();
            await this.storage.saveTask(task);
            await this.storage.updateTaskStatus(task.id, 'completed');
        }
        catch (error) {
            console.error(`Continue research failed for task ${task.id}:`, error);
            await this.storage.updateTaskStatus(task.id, 'failed');
            throw error;
        }
    }
}
// Main execution
async function main() {
    const server = new DeepResearchServer();
    await server.initialize();
    await server.run();
}
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch((error) => {
        console.error('Server error:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=index.js.map