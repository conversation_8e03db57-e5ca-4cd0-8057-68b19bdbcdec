import { v4 as uuidv4 } from 'uuid';
import { ResearchError } from './types.js';
export function generateId() {
    return uuidv4();
}
export function getCurrentTimestamp() {
    return new Date().toISOString();
}
export function sanitizeText(text) {
    return text
        .replace(/\s+/g, ' ')
        .replace(/[^\w\s\-.,!?;:()\[\]{}'"]/g, '')
        .trim();
}
export function extractDomain(url) {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname;
    }
    catch {
        return '';
    }
}
export function isValidUrl(url) {
    try {
        new URL(url);
        return true;
    }
    catch {
        return false;
    }
}
export function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength - 3) + '...';
}
export function calculateRelevanceScore(query, title, content) {
    const queryTerms = query.toLowerCase().split(/\s+/);
    const titleLower = title.toLowerCase();
    const contentLower = content.toLowerCase();
    let score = 0;
    let titleMatches = 0;
    let contentMatches = 0;
    for (const term of queryTerms) {
        if (term.length < 3)
            continue; // Skip very short terms
        // Title matches are weighted more heavily
        if (titleLower.includes(term)) {
            titleMatches++;
            score += 3;
        }
        // Content matches
        const contentMatchCount = (contentLower.match(new RegExp(term, 'g')) || []).length;
        contentMatches += contentMatchCount;
        score += contentMatchCount * 0.5;
    }
    // Normalize score based on content length and query complexity
    const contentLength = content.length;
    const normalizedScore = score / Math.max(queryTerms.length, 1);
    // Boost score for shorter, more focused content with high relevance
    if (contentLength < 2000 && titleMatches > 0) {
        return Math.min(normalizedScore * 1.2, 10);
    }
    return Math.min(normalizedScore, 10);
}
export function extractKeywords(text, maxKeywords = 10) {
    const words = text
        .toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 3);
    const wordFreq = new Map();
    for (const word of words) {
        wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
    }
    return Array.from(wordFreq.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, maxKeywords)
        .map(([word]) => word);
}
export function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
export async function retry(fn, maxAttempts = 3, delayMs = 1000) {
    let lastError;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        }
        catch (error) {
            lastError = error;
            if (attempt === maxAttempts) {
                break;
            }
            await sleep(delayMs * attempt);
        }
    }
    throw new ResearchError(`Failed after ${maxAttempts} attempts: ${lastError.message}`, 'RETRY_FAILED', { attempts: maxAttempts, lastError: lastError.message });
}
export function formatDuration(startTime, endTime) {
    const end = endTime || new Date();
    const durationMs = end.getTime() - startTime.getTime();
    const seconds = Math.floor(durationMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    }
    else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    }
    else {
        return `${seconds}s`;
    }
}
export function chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
        chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
}
export function removeDuplicateUrls(urls) {
    const seen = new Set();
    const unique = [];
    for (const url of urls) {
        const normalizedUrl = normalizeUrl(url);
        if (!seen.has(normalizedUrl)) {
            seen.add(normalizedUrl);
            unique.push(url);
        }
    }
    return unique;
}
export function normalizeUrl(url) {
    try {
        const urlObj = new URL(url);
        // Remove common tracking parameters
        const trackingParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'];
        trackingParams.forEach(param => urlObj.searchParams.delete(param));
        // Remove trailing slash
        urlObj.pathname = urlObj.pathname.replace(/\/$/, '') || '/';
        return urlObj.toString();
    }
    catch {
        return url;
    }
}
export function estimateReadingTime(text) {
    const wordsPerMinute = 200;
    const wordCount = text.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
}
export function createProgressMessage(step, progress, details) {
    const progressBar = '█'.repeat(Math.floor(progress / 5)) +
        '░'.repeat(20 - Math.floor(progress / 5));
    let message = `[${progressBar}] ${progress}% - ${step}`;
    if (details) {
        message += `\n${details}`;
    }
    return message;
}
export function validateConfig(config) {
    if (!config.ai?.provider) {
        throw new ResearchError('AI provider not configured', 'CONFIG_ERROR');
    }
    if (!config.search?.provider) {
        throw new ResearchError('Search provider not configured', 'CONFIG_ERROR');
    }
    if (config.ai.provider === 'openai' && !config.ai.api_key) {
        throw new ResearchError('OpenAI API key not provided', 'CONFIG_ERROR');
    }
    if (config.ai.provider === 'anthropic' && !config.ai.api_key) {
        throw new ResearchError('Anthropic API key not provided', 'CONFIG_ERROR');
    }
}
//# sourceMappingURL=utils.js.map