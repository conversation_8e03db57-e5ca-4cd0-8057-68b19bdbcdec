{"version": 3, "file": "synthesizer.js", "sourceRoot": "", "sources": ["../src/synthesizer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAmC,OAAO,EAAE,MAAM,YAAY,CAAC;AACtE,OAAO,EAAE,mBAAmB,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AAClF,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,SAAS,MAAM,mBAAmB,CAAC;AAE1C,MAAM,OAAO,mBAAmB;IACtB,QAAQ,CAAW;IACnB,MAAM,CAAU;IAChB,SAAS,CAAa;IAE9B,YAAY,QAAkB;QAC5B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEO,YAAY;QAClB,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC/B,KAAK,QAAQ;gBACX,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;oBAC3B,MAAM,IAAI,OAAO,CAAC,6BAA6B,CAAC,CAAC;gBACnD,CAAC;gBACD,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;oBACvB,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;oBAC7B,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ;iBAChC,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;oBAC3B,MAAM,IAAI,OAAO,CAAC,gCAAgC,CAAC,CAAC;gBACtD,CAAC;gBACD,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC;oBAC7B,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;iBAC9B,CAAC,CAAC;gBACH,MAAM;YACR;gBACE,MAAM,IAAI,OAAO,CAAC,4BAA4B,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,IAAkB;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,OAAO,CAAC,8BAA+B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,IAAkB;QAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ;aAC9B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,eAAe,CAAC;aACrD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhB,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe;aACrC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;aACrB,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,OAAO;;mBAEQ,IAAI,CAAC,KAAK;;;EAG3B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;oBAEvE,WAAW,CAAC,MAAM;EACpC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;UACb,CAAC,CAAC,MAAM;OACX,CAAC,CAAC,GAAG;SACH,CAAC,CAAC,KAAK;aACH,CAAC,CAAC,eAAe;WACnB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;CACrC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;EAGX,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAgDiC,CAAC;IAC7C,CAAC;IAEO,KAAK,CAAC,MAAM,CAAC,MAAc;QACjC,+CAA+C;QAC/C,MAAM,YAAY,GAAG;YACnB,GAAG,IAAI,CAAC,QAAQ;YAChB,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC;YACxD,WAAW,EAAE,GAAG,EAAE,gDAAgD;SACnE,CAAC;QAEF,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC/B,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAC/C,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAClD;gBACE,MAAM,IAAI,OAAO,CAAC,4BAA4B,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,MAAgB;QACvD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,OAAO,CAAC,+BAA+B,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACzD,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;YAC7C,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,WAAW,EAAE,MAAM,CAAC,WAAW;SAChC,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;QACtD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,MAAgB;QAC1D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,IAAI,OAAO,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;YACpD,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;SAC9C,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACvC,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB,CAAC;QAED,MAAM,IAAI,OAAO,CAAC,yCAAyC,CAAC,CAAC;IAC/D,CAAC;IAEO,iBAAiB,CAAC,MAAc,EAAE,IAAkB;QAC1D,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3B,MAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEpD,MAAM,QAAQ,GAAG;kBACH,IAAI,CAAC,KAAK;aACf,mBAAmB,EAAE;YACtB,QAAQ;WACT,IAAI,CAAC,QAAQ,CAAC,MAAM;kBACb,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI;;;CAG9E,CAAC;QAEE,OAAO,QAAQ,GAAG,MAAM,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,IAAkB;QAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ;aAC9B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,eAAe,CAAC;aACrD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhB,MAAM,MAAM,GAAG;;UAET,IAAI,CAAC,KAAK;;;EAGlB,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;0DAQ1B,CAAC;QAEvD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,OAAO,CAAC,yCAA0C,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,QAAmB;QAC3C,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,WAAW,GAAG,QAAQ;aACzB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,eAAe,CAAC;aACrD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhB,MAAM,MAAM,GAAG;;EAEjB,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;SACd,CAAC,CAAC,KAAK;UACN,CAAC,CAAC,MAAM;WACP,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;CACrC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;qDAQwC,CAAC;QAElD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAEpD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACrC,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAE/D,6CAA6C;YAC7C,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvD,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAE9C,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAC5B,+CAA+C,OAAO,eAAe,CACtE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,IAAkB;QAC9C,MAAM,MAAM,GAAG;;UAET,IAAI,CAAC,KAAK;;;EAGlB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;EAGjG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;sDAQX,CAAC;QAEnD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAEpD,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5C,OAAO,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO;gBACL,sDAAsD;gBACtD,4DAA4D;gBAC5D,uDAAuD;aACxD,CAAC;QACJ,CAAC;IACH,CAAC;CACF"}