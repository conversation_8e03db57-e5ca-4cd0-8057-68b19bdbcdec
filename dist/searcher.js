import axios from 'axios';
import { SearchError } from './types.js';
import { retry, getCurrentTimestamp, removeDuplicateUrls } from './utils.js';
export class SearchManager {
    config;
    constructor(config) {
        this.config = config;
    }
    async search(query, maxResults) {
        const limit = maxResults || this.config.max_results;
        try {
            switch (this.config.provider) {
                case 'google':
                    return await this.searchGoogle(query, limit);
                case 'bing':
                    return await this.searchBing(query, limit);
                case 'duckduckgo':
                    return await this.searchDuckDuckGo(query, limit);
                case 'serper':
                    return await this.searchSerper(query, limit);
                default:
                    throw new SearchError(`Unsupported search provider: ${this.config.provider}`);
            }
        }
        catch (error) {
            if (error instanceof SearchError) {
                throw error;
            }
            throw new SearchError(`Search failed: ${error.message}`, { query, provider: this.config.provider });
        }
    }
    async searchGoogle(query, maxResults) {
        if (!this.config.api_key) {
            throw new SearchError('Google Search API key not provided');
        }
        return retry(async () => {
            const response = await axios.get('https://www.googleapis.com/customsearch/v1', {
                params: {
                    key: this.config.api_key,
                    cx: process.env.GOOGLE_SEARCH_ENGINE_ID,
                    q: query,
                    num: Math.min(maxResults, 10),
                    safe: this.config.safe_search ? 'active' : 'off',
                },
                timeout: 10000,
            });
            if (!response.data.items) {
                return [];
            }
            return response.data.items.map((item) => ({
                title: item.title,
                url: item.link,
                snippet: item.snippet || '',
                source: 'google',
                timestamp: getCurrentTimestamp(),
            }));
        });
    }
    async searchBing(query, maxResults) {
        if (!this.config.api_key) {
            throw new SearchError('Bing Search API key not provided');
        }
        return retry(async () => {
            const response = await axios.get('https://api.bing.microsoft.com/v7.0/search', {
                headers: {
                    'Ocp-Apim-Subscription-Key': this.config.api_key,
                },
                params: {
                    q: query,
                    count: Math.min(maxResults, 50),
                    safeSearch: this.config.safe_search ? 'Strict' : 'Off',
                },
                timeout: 10000,
            });
            if (!response.data.webPages?.value) {
                return [];
            }
            return response.data.webPages.value.map((item) => ({
                title: item.name,
                url: item.url,
                snippet: item.snippet || '',
                source: 'bing',
                timestamp: getCurrentTimestamp(),
            }));
        });
    }
    async searchDuckDuckGo(query, maxResults) {
        return retry(async () => {
            // Using DuckDuckGo Instant Answer API (limited but free)
            const response = await axios.get('https://api.duckduckgo.com/', {
                params: {
                    q: query,
                    format: 'json',
                    no_html: '1',
                    skip_disambig: '1',
                },
                timeout: 10000,
            });
            const results = [];
            // Add abstract if available
            if (response.data.Abstract) {
                results.push({
                    title: response.data.Heading || query,
                    url: response.data.AbstractURL || '',
                    snippet: response.data.Abstract,
                    source: 'duckduckgo',
                    timestamp: getCurrentTimestamp(),
                });
            }
            // Add related topics
            if (response.data.RelatedTopics) {
                for (const topic of response.data.RelatedTopics.slice(0, maxResults - results.length)) {
                    if (topic.Text && topic.FirstURL) {
                        results.push({
                            title: topic.Text.split(' - ')[0] || topic.Text,
                            url: topic.FirstURL,
                            snippet: topic.Text,
                            source: 'duckduckgo',
                            timestamp: getCurrentTimestamp(),
                        });
                    }
                }
            }
            return results.slice(0, maxResults);
        });
    }
    async searchSerper(query, maxResults) {
        if (!this.config.api_key) {
            throw new SearchError('Serper API key not provided');
        }
        return retry(async () => {
            const response = await axios.post('https://google.serper.dev/search', {
                q: query,
                num: Math.min(maxResults, 100),
            }, {
                headers: {
                    'X-API-KEY': this.config.api_key,
                    'Content-Type': 'application/json',
                },
                timeout: 10000,
            });
            if (!response.data.organic) {
                return [];
            }
            return response.data.organic.map((item) => ({
                title: item.title,
                url: item.link,
                snippet: item.snippet || '',
                source: 'serper',
                timestamp: getCurrentTimestamp(),
            }));
        });
    }
    async searchMultiple(queries, maxResultsPerQuery = 5) {
        const allResults = [];
        for (const query of queries) {
            try {
                const results = await this.search(query, maxResultsPerQuery);
                allResults.push(...results);
            }
            catch (error) {
                console.warn(`Search failed for query "${query}":`, error);
                // Continue with other queries
            }
        }
        // Remove duplicates and return
        const uniqueUrls = removeDuplicateUrls(allResults.map(r => r.url));
        return allResults.filter(result => uniqueUrls.includes(result.url));
    }
    async searchWithFilters(query, options = {}) {
        let modifiedQuery = query;
        // Add domain filters
        if (options.excludeDomains) {
            for (const domain of options.excludeDomains) {
                modifiedQuery += ` -site:${domain}`;
            }
        }
        if (options.includeDomains) {
            const siteFilter = options.includeDomains.map(domain => `site:${domain}`).join(' OR ');
            modifiedQuery += ` (${siteFilter})`;
        }
        // Add file type filter
        if (options.fileType) {
            modifiedQuery += ` filetype:${options.fileType}`;
        }
        // Add language filter (Google specific)
        if (options.language && this.config.provider === 'google') {
            modifiedQuery += ` lang:${options.language}`;
        }
        return this.search(modifiedQuery);
    }
    generateSearchQueries(originalQuery) {
        const queries = [originalQuery];
        // Add variations
        queries.push(`"${originalQuery}"`); // Exact phrase
        queries.push(`${originalQuery} overview`);
        queries.push(`${originalQuery} analysis`);
        queries.push(`${originalQuery} research`);
        queries.push(`${originalQuery} study`);
        queries.push(`what is ${originalQuery}`);
        queries.push(`${originalQuery} definition`);
        queries.push(`${originalQuery} examples`);
        // Add related terms based on common patterns
        const words = originalQuery.split(' ');
        if (words.length > 1) {
            // Try different word combinations
            queries.push(words.slice(0, -1).join(' '));
            queries.push(words.slice(1).join(' '));
        }
        return queries.slice(0, 8); // Limit to prevent too many searches
    }
}
//# sourceMappingURL=searcher.js.map