export declare function generateId(): string;
export declare function getCurrentTimestamp(): string;
export declare function sanitizeText(text: string): string;
export declare function extractDomain(url: string): string;
export declare function isValidUrl(url: string): boolean;
export declare function truncateText(text: string, maxLength: number): string;
export declare function calculateRelevanceScore(query: string, title: string, content: string): number;
export declare function extractKeywords(text: string, maxKeywords?: number): string[];
export declare function sleep(ms: number): Promise<void>;
export declare function retry<T>(fn: () => Promise<T>, maxAttempts?: number, delayMs?: number): Promise<T>;
export declare function formatDuration(startTime: Date, endTime?: Date): string;
export declare function chunkArray<T>(array: T[], chunkSize: number): T[][];
export declare function removeDuplicateUrls(urls: string[]): string[];
export declare function normalizeUrl(url: string): string;
export declare function estimateReadingTime(text: string): number;
export declare function createProgressMessage(step: string, progress: number, details?: string): string;
export declare function validateConfig(config: any): void;
//# sourceMappingURL=utils.d.ts.map