# Deep Research MCP Server - Clara AI Setup (macOS)

## Overview

This guide helps you set up the Deep Research MCP Server to work with Clara AI + Ollama (DeepSeek R1) on macOS.

## ✅ Perfect Compatibility for Your Setup

- **✅ macOS**: Native support, optimized performance
- **✅ Clara AI**: Full MCP protocol support
- **✅ Ollama + DeepSeek R1**: Local AI processing
- **✅ Brave Browser**: No conflicts (server uses its own Chromium instance)
- **✅ Homebrew**: Easy installation and management

## 🚀 Quick Setup for Clara AI

### 1. Prerequisites (macOS)

```bash
# Install Node.js (if not already installed)
brew install node

# Verify Ollama is running (for local AI option)
ollama serve
# In another terminal: ollama pull deepseek-r1

# Verify Clara AI is installed and configured
```

### 2. Install and Configure

```bash
# Clone and setup
git clone <your-repo>
cd deep-research-mcp-server
npm install
npm run build

# Configure environment for your setup
cp .env.example .env
# Edit .env with your preferences (see below)
```

### 3. Recommended Configuration for Your Setup

**Option A: Use Local Ollama (Recommended for Privacy)**
```env
# AI Provider - Use your local Ollama
AI_PROVIDER=ollama
AI_BASE_URL=http://localhost:11434/v1
AI_MODEL=deepseek-r1:latest
AI_API_KEY=not_required_for_local
AI_MAX_TOKENS=3000
AI_TEMPERATURE=0.3

# Search Provider (free option)
SEARCH_PROVIDER=duckduckgo  # No API key needed
# OR use Serper for better results:
# SEARCH_PROVIDER=serper
# SEARCH_API_KEY=your_serper_key

# Browser settings (optimized for macOS)
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000

# Performance (conservative for local AI)
MAX_PAGES_PER_TASK=15
MAX_CONCURRENT_SEARCHES=2
```

**Option B: Use OpenAI (Faster, requires API key)**
```env
# AI Provider - Use OpenAI for faster processing
AI_PROVIDER=openai
AI_MODEL=gpt-3.5-turbo
AI_API_KEY=your_openai_api_key
AI_MAX_TOKENS=3000
AI_TEMPERATURE=0.3

# Search Provider
SEARCH_PROVIDER=serper  # Better results
SEARCH_API_KEY=your_serper_key

# Performance (can handle more with cloud AI)
MAX_PAGES_PER_TASK=25
MAX_CONCURRENT_SEARCHES=3
```

### 3. Start the Server

```bash
npm start
```

The server will output: `Deep Research MCP Server running on stdio`

## 🔌 Clara AI Integration

### Method 1: Direct MCP Connection (Recommended)

If Clara AI supports MCP servers directly:

1. **In Clara AI settings**, add MCP server:
   - **Server Type**: Local MCP Server
   - **Command**: `node`
   - **Args**: `["path/to/deep-research-mcp-server/dist/index.js"]`
   - **Working Directory**: `/path/to/deep-research-mcp-server`

### Method 2: HTTP Wrapper (If needed)

If Clara AI doesn't support direct MCP, I can create an HTTP wrapper:

```bash
# Create HTTP wrapper
npm run create-http-wrapper
npm run start-http

# Then connect Clara AI to: http://localhost:3001
```

## 🎯 Usage with Clara AI + DeepSeek R1

### Basic Research Query
```
Use the deep_research tool to investigate "latest developments in quantum computing 2024"
```

### Advanced Research with Focus
```
Research "AI impact on healthcare" focusing on:
- Medical diagnosis accuracy
- Drug discovery acceleration  
- Patient care automation
- Cost reduction potential

Exclude social media sources and limit to 25 pages.
```

### Monitor Progress
```
Check the status of research task [task-id] and show current progress
```

### Refine Research
```
Continue research task [task-id] with additional focus on "regulatory challenges and FDA approval processes"
```

## 🔧 Optimization for DeepSeek R1

Since Clara AI uses DeepSeek R1 for the main conversation, the MCP server handles:

- **Research Planning**: Uses OpenAI GPT-3.5-turbo (cost-effective)
- **Web Search**: Multiple providers (DuckDuckGo free, Serper premium)
- **Content Analysis**: Lightweight processing
- **Report Generation**: Structured markdown output

This creates a powerful combination:
- **DeepSeek R1** (via Clara AI): Main reasoning and conversation
- **MCP Server**: Specialized deep research capabilities

## 🛠️ Troubleshooting

### Common Issues

**1. Clara AI can't find MCP server**
```bash
# Verify server is running
npm start
# Check if process is listening
ps aux | grep node
```

**2. API key errors**
```bash
# Test configuration
node test-setup.js
# Verify API keys are valid
```

**3. Search not working**
```bash
# Try DuckDuckGo (no API key needed)
# Set SEARCH_PROVIDER=duckduckgo in .env
```

**4. Browser issues**
```bash
# Install browser dependencies (Linux)
sudo apt-get install -y chromium-browser

# Or use system Chrome
export PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome
```

### Performance Optimization

**For faster research:**
```env
# Reduce page processing
MAX_PAGES_PER_TASK=15
MAX_CONCURRENT_SEARCHES=3

# Faster browser
BROWSER_TIMEOUT=20000
```

**For comprehensive research:**
```env
# More thorough analysis
MAX_PAGES_PER_TASK=30
AI_MAX_TOKENS=4000
```

## 🎯 Best Practices with Clara AI

### 1. Research Workflow
```
1. Start research: "Research [topic] with focus on [areas]"
2. Monitor: "Check research progress for task [id]"
3. Refine: "Continue research with focus on [new areas]"
4. Analyze: DeepSeek R1 analyzes the generated report
```

### 2. Effective Queries
- **Be specific**: "AI in medical diagnosis 2024" vs "AI healthcare"
- **Set focus areas**: Helps narrow the research scope
- **Use exclusions**: Filter out irrelevant sources
- **Iterate**: Use continue_research to refine

### 3. Combining with DeepSeek R1
- **Research**: MCP server gathers comprehensive data
- **Analysis**: DeepSeek R1 provides deep reasoning
- **Synthesis**: Combine both for powerful insights

## 📊 Expected Performance

**Research Speed:**
- Simple queries: 3-5 minutes
- Complex research: 10-15 minutes
- Comprehensive analysis: 15-30 minutes

**Quality:**
- 15-30 web sources analyzed
- Relevance scoring and filtering
- Structured reports with citations
- Key insights and recommendations

## 🔄 Updates and Maintenance

```bash
# Update dependencies
npm update

# Rebuild after changes
npm run build

# Test configuration
node test-setup.js
```

## 🆘 Support

**If you encounter issues:**

1. **Check logs**: Server outputs detailed error messages
2. **Verify setup**: Run `node test-setup.js`
3. **Test components**: Try individual tools first
4. **API limits**: Check if you've hit rate limits
5. **Network**: Verify internet connectivity for searches

**Common Clara AI + MCP patterns:**
- Research → Analyze → Discuss → Refine → Report
- Background research while chatting with DeepSeek R1
- Fact-checking and source verification

The combination of Clara AI (DeepSeek R1) + Deep Research MCP Server provides powerful research capabilities that rival Google Gemini Deep Research! 🚀
